import 'package:flutter/foundation.dart';
import '../models/hadith.dart';

class HadithProvider with ChangeNotifier {
  List<Hadith> _hadiths = [];
  List<Hadith> _filteredHadiths = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = false;
  String _searchQuery = '';
  bool _showOnlyAuthentic = true;

  // Getters
  List<Hadith> get hadiths => _filteredHadiths;
  List<Hadith> get allHadiths => _hadiths;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  bool get showOnlyAuthentic => _showOnlyAuthentic;

  // الحصول على الفئات المتاحة
  List<String> get categories {
    final cats = _hadiths.map((h) => h.category).toSet().toList();
    cats.insert(0, 'الكل');
    return cats;
  }

  // الحصول على عدد الأحاديث في كل فئة
  Map<String, int> get categoryCounts {
    final counts = <String, int>{};
    for (final hadith in _hadiths) {
      if (!_showOnlyAuthentic || hadith.isAuthentic) {
        counts[hadith.category] = (counts[hadith.category] ?? 0) + 1;
      }
    }
    return counts;
  }

  // الحصول على المصادر المتاحة
  List<String> get sources {
    return _hadiths.map((h) => h.source).toSet().toList();
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    _isLoading = true;
    notifyListeners();

    try {
      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      _hadiths = _getSampleHadiths();
      _applyFilters();

    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الأحاديث: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تطبيق الفلاتر المحسنة
  void _applyFilters() {
    if (_searchQuery.isEmpty) {
      // بدون بحث - تطبيق الفلاتر العادية
      _filteredHadiths = _hadiths.where((hadith) {
        final matchesCategory = _selectedCategory == 'الكل' ||
                               hadith.category == _selectedCategory;
        final matchesAuthentic = !_showOnlyAuthentic || hadith.isAuthentic;
        return matchesCategory && matchesAuthentic && hadith.isAvailable;
      }).toList();

      // ترتيب حسب رقم الحديث
      _filteredHadiths.sort((a, b) => a.number.compareTo(b.number));
    } else {
      // مع البحث - استخدام البحث المتقدم
      final searchResults = searchHadiths(_searchQuery);
      _filteredHadiths = searchResults.where((hadith) {
        final matchesCategory = _selectedCategory == 'الكل' ||
                               hadith.category == _selectedCategory;
        final matchesAuthentic = !_showOnlyAuthentic || hadith.isAuthentic;
        return matchesCategory && matchesAuthentic;
      }).toList();
    }
  }

  // تغيير الفئة المختارة
  void setCategory(String category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilters();
      notifyListeners();
    }
  }

  // تحديث البحث
  void updateSearch(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      _applyFilters();
      notifyListeners();
    }
  }

  // تبديل عرض الأحاديث الصحيحة فقط
  void toggleAuthenticOnly() {
    _showOnlyAuthentic = !_showOnlyAuthentic;
    _applyFilters();
    notifyListeners();
  }

  // الحصول على حديث بالمعرف
  Hadith? getHadithById(String id) {
    try {
      return _hadiths.firstWhere((hadith) => hadith.id == id);
    } catch (e) {
      return null;
    }
  }

  // إضافة حديث جديد
  void addHadith(Hadith hadith) {
    _hadiths.add(hadith);
    _applyFilters();
    notifyListeners();
  }

  // تحديث حديث
  void updateHadith(Hadith updatedHadith) {
    final index = _hadiths.indexWhere((hadith) => hadith.id == updatedHadith.id);
    if (index != -1) {
      _hadiths[index] = updatedHadith;
      _applyFilters();
      notifyListeners();
    }
  }

  // حذف حديث
  void removeHadith(String id) {
    _hadiths.removeWhere((hadith) => hadith.id == id);
    _applyFilters();
    notifyListeners();
  }

  // البحث المتقدم في الأحاديث
  List<Hadith> searchHadiths(String query) {
    if (query.isEmpty) {
      return [];
    }

    // تنظيف وتحضير النص للبحث
    final cleanQuery = _cleanSearchText(query);
    final searchTerms = _extractSearchTerms(cleanQuery);

    if (searchTerms.isEmpty) {
      return [];
    }

    // البحث مع نظام النقاط
    final results = <MapEntry<Hadith, double>>[];

    for (final hadith in _hadiths) {
      if (!hadith.isAvailable) continue;

      final score = _calculateHadithScore(hadith, searchTerms, cleanQuery);
      if (score > 0) {
        results.add(MapEntry(hadith, score));
      }
    }

    // ترتيب حسب النقاط ثم حسب رقم الحديث
    results.sort((a, b) {
      final scoreComparison = b.value.compareTo(a.value);
      if (scoreComparison != 0) return scoreComparison;
      return a.key.number.compareTo(b.key.number);
    });

    return results.map((entry) => entry.key).toList();
  }

  // تنظيف النص للبحث مع الحفاظ على الأحرف العربية
  String _cleanSearchText(String text) {
    return text
        .trim()
        .toLowerCase()
        // إزالة علامات الترقيم فقط مع الحفاظ على جميع الأحرف العربية والإنجليزية
        .replaceAll(RegExp(r'[،؛؟!""''()[\]{}«»_=+|/:;.,?!"\'()]+'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ');
  }

  // استخراج مصطلحات البحث
  List<String> _extractSearchTerms(String query) {
    return query
        .split(' ')
        .where((term) => term.length >= 2)
        .toList();
  }

  // حساب نقاط الحديث مع البحث الدقيق بالكلمات
  double _calculateHadithScore(Hadith hadith, List<String> searchTerms, String fullQuery) {
    double score = 0.0;

    // النصوص للبحث فيها مع أوزانها
    final searchFields = {
      _cleanSearchText(hadith.arabicText): 15.0,
      _cleanSearchText(hadith.translation): 12.0,
      _cleanSearchText(hadith.theme): 10.0,
      _cleanSearchText(hadith.category): 8.0,
      _cleanSearchText(hadith.narrator): 6.0,
      _cleanSearchText(hadith.source): 5.0,
    };

    // البحث في الكلمات المفتاحية
    final keywordsText = _cleanSearchText(hadith.keywords.join(' '));
    if (keywordsText.isNotEmpty) {
      searchFields[keywordsText] = 7.0;
    }

    // البحث الدقيق بالكلمات الكاملة
    for (final entry in searchFields.entries) {
      final fieldWords = entry.key.split(' ');

      // البحث عن التطابق الكامل للعبارة
      if (entry.key.contains(fullQuery)) {
        score += entry.value * 3.0; // نقاط عالية للتطابق الكامل
      }

      // البحث عن كل مصطلح ككلمة كاملة
      for (final term in searchTerms) {
        if (_containsWholeWord(entry.key, term)) {
          // نقاط إضافية إذا كانت الكلمة في بداية النص
          final multiplier = fieldWords.isNotEmpty && fieldWords.first == term ? 2.0 : 1.0;
          score += entry.value * multiplier;
        }
      }
    }

    // يجب أن تحتوي على جميع المصطلحات المطلوبة
    final requiredMatches = searchTerms.where((term) {
      return searchFields.keys.any((field) => _containsWholeWord(field, term));
    }).length;

    // إذا لم تحتوي على جميع المصطلحات، لا نقاط
    if (requiredMatches < searchTerms.length) {
      return 0.0;
    }

    // نقاط إضافية للأحاديث الصحيحة
    if (hadith.isAuthentic) {
      score += 5.0;
    }

    // نقاط إضافية للتطابق المتعدد
    if (requiredMatches > 1) {
      score += requiredMatches * 3.0;
    }

    return score;
  }

  // فحص وجود كلمة كاملة في النص مع دعم أفضل للعربية
  bool _containsWholeWord(String text, String word) {
    if (text.isEmpty || word.isEmpty) return false;

    // تقسيم النص إلى كلمات باستخدام المسافات وعلامات الترقيم
    final words = text.split(RegExp(r'[\s،؛؟!""''()[\]{}«»_=+|/:;.,?!"\'()]+'));

    // البحث عن تطابق كامل للكلمة (مع تجاهل الكلمات الفارغة)
    return words.where((w) => w.isNotEmpty).any((w) => w == word);
  }

  // بيانات شاملة للأحاديث النبوية
  List<Hadith> _getSampleHadiths() {
    return [
      // أحاديث العقيدة
      const Hadith(
        id: 'hadith_1',
        arabicText: 'إِنَّمَا الْأَعْمَالُ بِالنِّيَّاتِ، وَإِنَّمَا لِكُلِّ امْرِئٍ مَا نَوَى، فَمَنْ كَانَتْ هِجْرَتُهُ إِلَى اللَّهِ وَرَسُولِهِ فَهِجْرَتُهُ إِلَى اللَّهِ وَرَسُولِهِ، وَمَنْ كَانَتْ هِجْرَتُهُ لِدُنْيَا يُصِيبُهَا أَوْ امْرَأَةٍ يَنْكِحُهَا فَهِجْرَتُهُ إِلَى مَا هَاجَرَ إِلَيْهِ',
        translation: 'إنما الأعمال بالنيات، وإنما لكل امرئ ما نوى، فمن كانت هجرته إلى الله ورسوله فهجرته إلى الله ورسوله، ومن كانت هجرته لدنيا يصيبها أو امرأة ينكحها فهجرته إلى ما هاجر إليه',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'النية في الأعمال',
        number: 1,
        keywords: ['النية', 'الأعمال', 'القصد', 'الهجرة'],
      ),
      const Hadith(
        id: 'hadith_2',
        arabicText: 'بُنِيَ الْإِسْلَامُ عَلَى خَمْسٍ: شَهَادَةِ أَنْ لَا إِلَهَ إِلَّا اللَّهُ وَأَنَّ مُحَمَّدًا رَسُولُ اللَّهِ، وَإِقَامِ الصَّلَاةِ، وَإِيتَاءِ الزَّكَاةِ، وَالْحَجِّ، وَصَوْمِ رَمَضَانَ',
        translation: 'بني الإسلام على خمس: شهادة أن لا إله إلا الله وأن محمداً رسول الله، وإقام الصلاة، وإيتاء الزكاة، والحج، وصوم رمضان',
        narrator: 'عبد الله بن عمر رضي الله عنهما',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'أركان الإسلام',
        number: 2,
        keywords: ['الإسلام', 'الأركان', 'الشهادة', 'الصلاة', 'الزكاة', 'الحج', 'الصوم'],
      ),
      const Hadith(
        id: 'hadith_3',
        arabicText: 'الْإِيمَانُ أَنْ تُؤْمِنَ بِاللَّهِ وَمَلَائِكَتِهِ وَكُتُبِهِ وَرُسُلِهِ وَالْيَوْمِ الْآخِرِ وَتُؤْمِنَ بِالْقَدَرِ خَيْرِهِ وَشَرِّهِ',
        translation: 'الإيمان أن تؤمن بالله وملائكته وكتبه ورسله واليوم الآخر وتؤمن بالقدر خيره وشره',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العقيدة',
        theme: 'أركان الإيمان',
        number: 3,
        keywords: ['الإيمان', 'الله', 'الملائكة', 'الكتب', 'الرسل', 'اليوم الآخر', 'القدر'],
      ),
      const Hadith(
        id: 'hadith_4',
        arabicText: 'مَنْ قَالَ لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ، فِي يَوْمٍ مِائَةَ مَرَّةٍ كَانَتْ لَهُ عَدْلَ عَشْرِ رِقَابٍ',
        translation: 'من قال لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير، في يوم مائة مرة كانت له عدل عشر رقاب',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'فضل التهليل',
        number: 4,
        keywords: ['التهليل', 'التوحيد', 'الذكر', 'الأجر'],
      ),
      const Hadith(
        id: 'hadith_5',
        arabicText: 'كَلِمَتَانِ خَفِيفَتَانِ عَلَى اللِّسَانِ، ثَقِيلَتَانِ فِي الْمِيزَانِ، حَبِيبَتَانِ إِلَى الرَّحْمَنِ: سُبْحَانَ اللَّهِ وَبِحَمْدِهِ، سُبْحَانَ اللَّهِ الْعَظِيمِ',
        translation: 'كلمتان خفيفتان على اللسان، ثقيلتان في الميزان، حبيبتان إلى الرحمن: سبحان الله وبحمده، سبحان الله العظيم',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'فضل التسبيح',
        number: 5,
        keywords: ['التسبيح', 'الذكر', 'الميزان', 'الأجر'],
      ),

      // أحاديث العبادة
      const Hadith(
        id: 'hadith_6',
        arabicText: 'الصَّلَاةُ عِمَادُ الدِّينِ، فَمَنْ أَقَامَهَا أَقَامَ الدِّينَ، وَمَنْ هَدَمَهَا هَدَمَ الدِّينَ',
        translation: 'الصلاة عماد الدين، فمن أقامها أقام الدين، ومن هدمها هدم الدين',
        narrator: 'عمر بن الخطاب رضي الله عنه',
        source: 'البيهقي',
        category: 'أحاديث العبادة',
        theme: 'أهمية الصلاة',
        number: 6,
        keywords: ['الصلاة', 'عماد الدين', 'العبادة'],
      ),
      const Hadith(
        id: 'hadith_7',
        arabicText: 'صُومُوا تَصِحُّوا',
        translation: 'صوموا تصحوا',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'الطبراني',
        category: 'أحاديث العبادة',
        theme: 'فوائد الصوم',
        number: 7,
        keywords: ['الصوم', 'الصحة', 'العبادة'],
      ),
      const Hadith(
        id: 'hadith_8',
        arabicText: 'مَنْ حَجَّ فَلَمْ يَرْفُثْ وَلَمْ يَفْسُقْ رَجَعَ كَيَوْمِ وَلَدَتْهُ أُمُّهُ',
        translation: 'من حج فلم يرفث ولم يفسق رجع كيوم ولدته أمه',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العبادة',
        theme: 'فضل الحج',
        number: 8,
        keywords: ['الحج', 'المغفرة', 'التوبة'],
      ),
      const Hadith(
        id: 'hadith_9',
        arabicText: 'مَا نَقَصَتْ صَدَقَةٌ مِنْ مَالٍ، وَمَا زَادَ اللَّهُ عَبْدًا بِعَفْوٍ إِلَّا عِزًّا، وَمَا تَوَاضَعَ أَحَدٌ لِلَّهِ إِلَّا رَفَعَهُ اللَّهُ',
        translation: 'ما نقصت صدقة من مال، وما زاد الله عبداً بعفو إلا عزاً، وما تواضع أحد لله إلا رفعه الله',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل الصدقة والعفو',
        number: 9,
        keywords: ['الصدقة', 'العفو', 'التواضع', 'البركة'],
      ),
      const Hadith(
        id: 'hadith_10',
        arabicText: 'اقْرَءُوا الْقُرْآنَ فَإِنَّهُ يَأْتِي يَوْمَ الْقِيَامَةِ شَفِيعًا لِأَصْحَابِهِ',
        translation: 'اقرءوا القرآن فإنه يأتي يوم القيامة شفيعاً لأصحابه',
        narrator: 'أبو أمامة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل قراءة القرآن',
        number: 10,
        keywords: ['القرآن', 'الشفاعة', 'القراءة'],
      ),

      // أحاديث الأخلاق
      const Hadith(
        id: 'hadith_11',
        arabicText: 'الْمُسْلِمُ مَنْ سَلِمَ الْمُسْلِمُونَ مِنْ لِسَانِهِ وَيَدِهِ، وَالْمُهَاجِرُ مَنْ هَجَرَ مَا نَهَى اللَّهُ عَنْهُ',
        translation: 'المسلم من سلم المسلمون من لسانه ويده، والمهاجر من هجر ما نهى الله عنه',
        narrator: 'عبد الله بن عمرو رضي الله عنهما',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'صفات المسلم الحق',
        number: 11,
        keywords: ['المسلم', 'الأذى', 'اللسان', 'اليد', 'الهجرة'],
      ),
      const Hadith(
        id: 'hadith_12',
        arabicText: 'إِنَّمَا بُعِثْتُ لِأُتَمِّمَ مَكَارِمَ الْأَخْلَاقِ',
        translation: 'إنما بعثت لأتمم مكارم الأخلاق',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'البخاري في الأدب المفرد',
        category: 'أحاديث الأخلاق',
        theme: 'مقصد البعثة النبوية',
        number: 12,
        keywords: ['الأخلاق', 'البعثة', 'مكارم'],
      ),
      const Hadith(
        id: 'hadith_13',
        arabicText: 'الْبِرُّ حُسْنُ الْخُلُقِ، وَالْإِثْمُ مَا حَاكَ فِي صَدْرِكَ وَكَرِهْتَ أَنْ يَطَّلِعَ عَلَيْهِ النَّاسُ',
        translation: 'البر حسن الخلق، والإثم ما حاك في صدرك وكرهت أن يطلع عليه الناس',
        narrator: 'النواس بن سمعان رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث الأخلاق',
        theme: 'تعريف البر والإثم',
        number: 13,
        keywords: ['البر', 'الخلق', 'الإثم', 'الضمير'],
      ),
      const Hadith(
        id: 'hadith_14',
        arabicText: 'مَنْ كَانَ يُؤْمِنُ بِاللَّهِ وَالْيَوْمِ الْآخِرِ فَلْيَقُلْ خَيْرًا أَوْ لِيَصْمُتْ',
        translation: 'من كان يؤمن بالله واليوم الآخر فليقل خيراً أو ليصمت',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'آداب الكلام',
        number: 14,
        keywords: ['الكلام', 'الخير', 'الصمت', 'الإيمان'],
      ),
      const Hadith(
        id: 'hadith_15',
        arabicText: 'لَيْسَ الْمُؤْمِنُ بِالطَّعَّانِ وَلَا اللَّعَّانِ وَلَا الْفَاحِشِ وَلَا الْبَذِيءِ',
        translation: 'ليس المؤمن بالطعان ولا اللعان ولا الفاحش ولا البذيء',
        narrator: 'عبد الله بن مسعود رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث الأخلاق',
        theme: 'صفات المؤمن',
        number: 15,
        keywords: ['المؤمن', 'الطعن', 'اللعن', 'الفحش'],
      ),
      const Hadith(
        id: 'hadith_16',
        arabicText: 'لَا يُؤْمِنُ أَحَدُكُمْ حَتَّى يُحِبَّ لِأَخِيهِ مَا يُحِبُّ لِنَفْسِهِ',
        translation: 'لا يؤمن أحدكم حتى يحب لأخيه ما يحب لنفسه',
        narrator: 'أنس بن مالك رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'المحبة والأخوة',
        number: 16,
        keywords: ['الإيمان', 'المحبة', 'الأخوة', 'الإيثار'],
      ),
      const Hadith(
        id: 'hadith_17',
        arabicText: 'الدِّينُ النَّصِيحَةُ، قُلْنَا: لِمَنْ؟ قَالَ: لِلَّهِ وَلِكِتَابِهِ وَلِرَسُولِهِ وَلِأَئِمَّةِ الْمُسْلِمِينَ وَعَامَّتِهِمْ',
        translation: 'الدين النصيحة، قلنا: لمن؟ قال: لله ولكتابه ولرسوله ولأئمة المسلمين وعامتهم',
        narrator: 'تميم الداري رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث الأخلاق',
        theme: 'النصيحة',
        number: 17,
        keywords: ['النصيحة', 'الدين', 'الإخلاص'],
      ),
      const Hadith(
        id: 'hadith_18',
        arabicText: 'مَنْ لَا يَرْحَمُ النَّاسَ لَا يَرْحَمُهُ اللَّهُ',
        translation: 'من لا يرحم الناس لا يرحمه الله',
        narrator: 'جرير بن عبد الله رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'الرحمة',
        number: 18,
        keywords: ['الرحمة', 'التراحم', 'الإحسان'],
      ),
      const Hadith(
        id: 'hadith_19',
        arabicText: 'إِنَّ اللَّهَ كَتَبَ الْإِحْسَانَ عَلَى كُلِّ شَيْءٍ',
        translation: 'إن الله كتب الإحسان على كل شيء',
        narrator: 'شداد بن أوس رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث الأخلاق',
        theme: 'الإحسان',
        number: 19,
        keywords: ['الإحسان', 'الرفق', 'الجودة'],
      ),
      const Hadith(
        id: 'hadith_20',
        arabicText: 'مَنْ كَانَ فِي حَاجَةِ أَخِيهِ كَانَ اللَّهُ فِي حَاجَتِهِ',
        translation: 'من كان في حاجة أخيه كان الله في حاجته',
        narrator: 'عبد الله بن عمر رضي الله عنهما',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'قضاء الحوائج',
        number: 20,
        keywords: ['الحاجة', 'المساعدة', 'الأخوة'],
      ),

      // أحاديث المعاملات
      const Hadith(
        id: 'hadith_21',
        arabicText: 'التَّاجِرُ الصَّدُوقُ الْأَمِينُ مَعَ النَّبِيِّينَ وَالصِّدِّيقِينَ وَالشُّهَدَاءِ',
        translation: 'التاجر الصدوق الأمين مع النبيين والصديقين والشهداء',
        narrator: 'أبو سعيد الخدري رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث المعاملات',
        theme: 'التجارة الشريفة',
        number: 21,
        keywords: ['التجارة', 'الصدق', 'الأمانة'],
      ),
      const Hadith(
        id: 'hadith_22',
        arabicText: 'مَنْ غَشَّنَا فَلَيْسَ مِنَّا',
        translation: 'من غشنا فليس منا',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث المعاملات',
        theme: 'تحريم الغش',
        number: 22,
        keywords: ['الغش', 'الخداع', 'الأمانة'],
      ),
      const Hadith(
        id: 'hadith_23',
        arabicText: 'الْبَيِّعَانِ بِالْخِيَارِ مَا لَمْ يَتَفَرَّقَا',
        translation: 'البيعان بالخيار ما لم يتفرقا',
        narrator: 'عبد الله بن عمر رضي الله عنهما',
        source: 'صحيح البخاري',
        category: 'أحاديث المعاملات',
        theme: 'خيار المجلس',
        number: 23,
        keywords: ['البيع', 'الخيار', 'العقود'],
      ),
      const Hadith(
        id: 'hadith_24',
        arabicText: 'لَا ضَرَرَ وَلَا ضِرَارَ',
        translation: 'لا ضرر ولا ضرار',
        narrator: 'عبادة بن الصامت رضي الله عنه',
        source: 'ابن ماجه',
        category: 'أحاديث المعاملات',
        theme: 'رفع الضرر',
        number: 24,
        keywords: ['الضرر', 'العدالة', 'الحقوق'],
      ),
      const Hadith(
        id: 'hadith_25',
        arabicText: 'مَطْلُ الْغَنِيِّ ظُلْمٌ',
        translation: 'مطل الغني ظلم',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث المعاملات',
        theme: 'الوفاء بالدين',
        number: 25,
        keywords: ['المطل', 'الدين', 'الظلم'],
      ),

      // أحاديث إضافية متنوعة
      const Hadith(
        id: 'hadith_26',
        arabicText: 'خَيْرُ النَّاسِ أَنْفَعُهُمْ لِلنَّاسِ',
        translation: 'خير الناس أنفعهم للناس',
        narrator: 'جابر بن عبد الله رضي الله عنه',
        source: 'الطبراني',
        category: 'أحاديث الأخلاق',
        theme: 'النفع والخير',
        number: 26,
        keywords: ['الخير', 'النفع', 'الإحسان'],
      ),
      const Hadith(
        id: 'hadith_27',
        arabicText: 'اتَّقِ اللَّهَ حَيْثُمَا كُنْتَ، وَأَتْبِعِ السَّيِّئَةَ الْحَسَنَةَ تَمْحُهَا، وَخَالِقِ النَّاسَ بِخُلُقٍ حَسَنٍ',
        translation: 'اتق الله حيثما كنت، وأتبع السيئة الحسنة تمحها، وخالق الناس بخلق حسن',
        narrator: 'أبو ذر الغفاري رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث الأخلاق',
        theme: 'التقوى والخلق',
        number: 27,
        keywords: ['التقوى', 'الخلق', 'الحسنات'],
      ),
      const Hadith(
        id: 'hadith_28',
        arabicText: 'مَنْ صَلَّى الْفَجْرَ فَهُوَ فِي ذِمَّةِ اللَّهِ',
        translation: 'من صلى الفجر فهو في ذمة الله',
        narrator: 'جندب بن عبد الله رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل صلاة الفجر',
        number: 28,
        keywords: ['الفجر', 'الصلاة', 'الحماية'],
      ),
      const Hadith(
        id: 'hadith_29',
        arabicText: 'مَنْ قَامَ لَيْلَةَ الْقَدْرِ إِيمَانًا وَاحْتِسَابًا غُفِرَ لَهُ مَا تَقَدَّمَ مِنْ ذَنْبِهِ',
        translation: 'من قام ليلة القدر إيماناً واحتساباً غفر له ما تقدم من ذنبه',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العبادة',
        theme: 'فضل ليلة القدر',
        number: 29,
        keywords: ['ليلة القدر', 'القيام', 'المغفرة'],
      ),
      const Hadith(
        id: 'hadith_30',
        arabicText: 'مَنْ صَامَ رَمَضَانَ إِيمَانًا وَاحْتِسَابًا غُفِرَ لَهُ مَا تَقَدَّمَ مِنْ ذَنْبِهِ',
        translation: 'من صام رمضان إيماناً واحتساباً غفر له ما تقدم من ذنبه',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العبادة',
        theme: 'فضل صيام رمضان',
        number: 30,
        keywords: ['رمضان', 'الصيام', 'المغفرة'],
      ),
      const Hadith(
        id: 'hadith_31',
        arabicText: 'الطُّهُورُ شَطْرُ الْإِيمَانِ',
        translation: 'الطهور شطر الإيمان',
        narrator: 'أبو مالك الأشعري رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'أهمية الطهارة',
        number: 31,
        keywords: ['الطهارة', 'الإيمان', 'النظافة'],
      ),
      const Hadith(
        id: 'hadith_32',
        arabicText: 'بَيْنَ الرَّجُلِ وَبَيْنَ الشِّرْكِ وَالْكُفْرِ تَرْكُ الصَّلَاةِ',
        translation: 'بين الرجل وبين الشرك والكفر ترك الصلاة',
        narrator: 'جابر بن عبد الله رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العقيدة',
        theme: 'خطورة ترك الصلاة',
        number: 32,
        keywords: ['الصلاة', 'الكفر', 'الشرك'],
      ),
      const Hadith(
        id: 'hadith_33',
        arabicText: 'مَنْ قَالَ سُبْحَانَ اللَّهِ وَبِحَمْدِهِ فِي يَوْمٍ مِائَةَ مَرَّةٍ حُطَّتْ خَطَايَاهُ وَإِنْ كَانَتْ مِثْلَ زَبَدِ الْبَحْرِ',
        translation: 'من قال سبحان الله وبحمده في يوم مائة مرة حطت خطاياه وإن كانت مثل زبد البحر',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'فضل التسبيح',
        number: 33,
        keywords: ['التسبيح', 'الذكر', 'المغفرة'],
      ),
      const Hadith(
        id: 'hadith_34',
        arabicText: 'مَنْ تَوَضَّأَ فَأَحْسَنَ الْوُضُوءَ خَرَجَتْ خَطَايَاهُ مِنْ جَسَدِهِ حَتَّى تَخْرُجَ مِنْ تَحْتِ أَظْفَارِهِ',
        translation: 'من توضأ فأحسن الوضوء خرجت خطاياه من جسده حتى تخرج من تحت أظفاره',
        narrator: 'عثمان بن عفان رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل الوضوء',
        number: 34,
        keywords: ['الوضوء', 'الطهارة', 'المغفرة'],
      ),
      const Hadith(
        id: 'hadith_35',
        arabicText: 'مَنْ كَذَبَ عَلَيَّ مُتَعَمِّدًا فَلْيَتَبَوَّأْ مَقْعَدَهُ مِنَ النَّارِ',
        translation: 'من كذب علي متعمداً فليتبوأ مقعده من النار',
        narrator: 'علي بن أبي طالب رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث العقيدة',
        theme: 'تحريم الكذب على النبي',
        number: 35,
        keywords: ['الكذب', 'الحديث', 'التحذير'],
      ),

      // أحاديث إضافية لإكمال الخمسين
      const Hadith(
        id: 'hadith_36',
        arabicText: 'مَنْ أَحْيَا أَرْضًا مَيْتَةً فَهِيَ لَهُ',
        translation: 'من أحيا أرضاً ميتة فهي له',
        narrator: 'عائشة رضي الله عنها',
        source: 'صحيح البخاري',
        category: 'أحاديث المعاملات',
        theme: 'إحياء الأرض',
        number: 36,
        keywords: ['الأرض', 'الإحياء', 'الملكية'],
      ),
      const Hadith(
        id: 'hadith_37',
        arabicText: 'الْمُؤْمِنُ مِرْآةُ الْمُؤْمِنِ',
        translation: 'المؤمن مرآة المؤمن',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'أبو داود',
        category: 'أحاديث الأخلاق',
        theme: 'الأخوة الإيمانية',
        number: 37,
        keywords: ['المؤمن', 'المرآة', 'الأخوة'],
      ),
      const Hadith(
        id: 'hadith_38',
        arabicText: 'مَنْ سَتَرَ مُسْلِمًا سَتَرَهُ اللَّهُ فِي الدُّنْيَا وَالْآخِرَةِ',
        translation: 'من ستر مسلماً ستره الله في الدنيا والآخرة',
        narrator: 'عبد الله بن عمر رضي الله عنهما',
        source: 'صحيح مسلم',
        category: 'أحاديث الأخلاق',
        theme: 'ستر العيوب',
        number: 38,
        keywords: ['الستر', 'العيوب', 'الرحمة'],
      ),
      const Hadith(
        id: 'hadith_39',
        arabicText: 'مَنْ نَفَّسَ عَنْ مُؤْمِنٍ كُرْبَةً مِنْ كُرَبِ الدُّنْيَا نَفَّسَ اللَّهُ عَنْهُ كُرْبَةً مِنْ كُرَبِ يَوْمِ الْقِيَامَةِ',
        translation: 'من نفس عن مؤمن كربة من كرب الدنيا نفس الله عنه كربة من كرب يوم القيامة',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث الأخلاق',
        theme: 'تفريج الكرب',
        number: 39,
        keywords: ['الكربة', 'التفريج', 'المساعدة'],
      ),
      const Hadith(
        id: 'hadith_40',
        arabicText: 'مَنْ يَسَّرَ عَلَى مُعْسِرٍ يَسَّرَ اللَّهُ عَلَيْهِ فِي الدُّنْيَا وَالْآخِرَةِ',
        translation: 'من يسر على معسر يسر الله عليه في الدنيا والآخرة',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث المعاملات',
        theme: 'التيسير على المعسر',
        number: 40,
        keywords: ['التيسير', 'المعسر', 'الرحمة'],
      ),
      const Hadith(
        id: 'hadith_41',
        arabicText: 'الْكَلِمَةُ الطَّيِّبَةُ صَدَقَةٌ',
        translation: 'الكلمة الطيبة صدقة',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح البخاري',
        category: 'أحاديث الأخلاق',
        theme: 'الكلام الطيب',
        number: 41,
        keywords: ['الكلمة', 'الطيبة', 'الصدقة'],
      ),
      const Hadith(
        id: 'hadith_42',
        arabicText: 'تَبَسُّمُكَ فِي وَجْهِ أَخِيكَ صَدَقَةٌ',
        translation: 'تبسمك في وجه أخيك صدقة',
        narrator: 'أبو ذر الغفاري رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث الأخلاق',
        theme: 'البشاشة والتبسم',
        number: 42,
        keywords: ['التبسم', 'البشاشة', 'الصدقة'],
      ),
      const Hadith(
        id: 'hadith_43',
        arabicText: 'مَنْ عَادَ مَرِيضًا أَوْ زَارَ أَخًا لَهُ فِي اللَّهِ نَادَاهُ مُنَادٍ: أَنْ طِبْتَ وَطَابَ مَمْشَاكَ وَتَبَوَّأْتَ مِنَ الْجَنَّةِ مَنْزِلًا',
        translation: 'من عاد مريضاً أو زار أخاً له في الله ناداه مناد: أن طبت وطاب ممشاك وتبوأت من الجنة منزلاً',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث الأخلاق',
        theme: 'عيادة المريض والزيارة',
        number: 43,
        keywords: ['العيادة', 'الزيارة', 'الأجر'],
      ),
      const Hadith(
        id: 'hadith_44',
        arabicText: 'مَنْ أَكَلَ طَعَامًا فَقَالَ: الْحَمْدُ لِلَّهِ الَّذِي أَطْعَمَنِي هَذَا وَرَزَقَنِيهِ مِنْ غَيْرِ حَوْلٍ مِنِّي وَلَا قُوَّةٍ، غُفِرَ لَهُ مَا تَقَدَّمَ مِنْ ذَنْبِهِ',
        translation: 'من أكل طعاماً فقال: الحمد لله الذي أطعمني هذا ورزقنيه من غير حول مني ولا قوة، غفر له ما تقدم من ذنبه',
        narrator: 'معاذ بن أنس رضي الله عنه',
        source: 'أبو داود',
        category: 'أحاديث العبادة',
        theme: 'آداب الطعام',
        number: 44,
        keywords: ['الطعام', 'الحمد', 'الشكر'],
      ),
      const Hadith(
        id: 'hadith_45',
        arabicText: 'مَنْ قَالَ حِينَ يُصْبِحُ وَحِينَ يُمْسِي: سُبْحَانَ اللَّهِ وَبِحَمْدِهِ مِائَةَ مَرَّةٍ لَمْ يَأْتِ أَحَدٌ يَوْمَ الْقِيَامَةِ بِأَفْضَلَ مِمَّا جَاءَ بِهِ',
        translation: 'من قال حين يصبح وحين يمسي: سبحان الله وبحمده مائة مرة لم يأت أحد يوم القيامة بأفضل مما جاء به',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'أذكار الصباح والمساء',
        number: 45,
        keywords: ['الأذكار', 'الصباح', 'المساء'],
      ),
      const Hadith(
        id: 'hadith_46',
        arabicText: 'مَنْ قَرَأَ آيَةَ الْكُرْسِيِّ دُبُرَ كُلِّ صَلَاةٍ مَكْتُوبَةٍ لَمْ يَمْنَعْهُ مِنْ دُخُولِ الْجَنَّةِ إِلَّا أَنْ يَمُوتَ',
        translation: 'من قرأ آية الكرسي دبر كل صلاة مكتوبة لم يمنعه من دخول الجنة إلا أن يموت',
        narrator: 'أبو أمامة رضي الله عنه',
        source: 'النسائي',
        category: 'أحاديث العبادة',
        theme: 'فضل آية الكرسي',
        number: 46,
        keywords: ['آية الكرسي', 'الصلاة', 'الجنة'],
      ),
      const Hadith(
        id: 'hadith_47',
        arabicText: 'مَنْ صَلَّى عَلَيَّ صَلَاةً صَلَّى اللَّهُ عَلَيْهِ بِهَا عَشْرًا',
        translation: 'من صلى علي صلاة صلى الله عليه بها عشراً',
        narrator: 'عبد الله بن عمرو رضي الله عنهما',
        source: 'صحيح مسلم',
        category: 'أحاديث العبادة',
        theme: 'فضل الصلاة على النبي',
        number: 47,
        keywords: ['الصلاة على النبي', 'الأجر', 'البركة'],
      ),
      const Hadith(
        id: 'hadith_48',
        arabicText: 'مَنْ قَالَ لَا إِلَهَ إِلَّا اللَّهُ وَاللَّهُ أَكْبَرُ صَدَّقَهُ رَبُّهُ فَقَالَ: لَا إِلَهَ إِلَّا أَنَا وَأَنَا أَكْبَرُ',
        translation: 'من قال لا إله إلا الله والله أكبر صدقه ربه فقال: لا إله إلا أنا وأنا أكبر',
        narrator: 'أبو هريرة رضي الله عنه',
        source: 'الترمذي',
        category: 'أحاديث العقيدة',
        theme: 'فضل التهليل والتكبير',
        number: 48,
        keywords: ['التهليل', 'التكبير', 'الذكر'],
      ),
      const Hadith(
        id: 'hadith_49',
        arabicText: 'مَنْ قَالَ أَسْتَغْفِرُ اللَّهَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ وَأَتُوبُ إِلَيْهِ غُفِرَ لَهُ وَإِنْ كَانَ فَرَّ مِنَ الزَّحْفِ',
        translation: 'من قال أستغفر الله الذي لا إله إلا هو الحي القيوم وأتوب إليه غفر له وإن كان فر من الزحف',
        narrator: 'بلال بن يسار رضي الله عنه',
        source: 'أبو داود',
        category: 'أحاديث العبادة',
        theme: 'سيد الاستغفار',
        number: 49,
        keywords: ['الاستغفار', 'التوبة', 'المغفرة'],
      ),
      const Hadith(
        id: '50',
        arabicText: 'مَنْ حَافَظَ عَلَى أَرْبَعِ رَكَعَاتٍ قَبْلَ الظُّهْرِ وَأَرْبَعٍ بَعْدَهَا حَرَّمَهُ اللَّهُ عَلَى النَّارِ',
        translation: 'من حافظ على أربع ركعات قبل الظهر وأربع بعدها حرمه الله على النار',
        narrator: 'أم حبيبة رضي الله عنها',
        source: 'أبو داود',
        category: 'أحاديث العبادة',
        theme: 'فضل سنة الظهر',
        number: 50,
        keywords: ['السنة', 'الظهر', 'النار'],
      ),
    ];
  }
}
