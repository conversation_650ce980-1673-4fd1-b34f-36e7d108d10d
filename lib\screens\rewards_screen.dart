import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/rewards_provider.dart';
import '../providers/theme_provider.dart';
import '../models/reward.dart';
import '../theme/app_theme.dart';


class RewardsScreen extends StatefulWidget {
  const RewardsScreen({super.key});

  @override
  State<RewardsScreen> createState() => _RewardsScreenState();
}

class _RewardsScreenState extends State<RewardsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<RewardsProvider, ThemeProvider>(
      builder: (context, rewardsProvider, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'المكافآت والإنجازات',
              style: themeProvider.applyFontSize(
                Theme.of(context).textTheme.titleLarge!,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode
                ? AppTheme.primaryDark
                : AppTheme.primaryLight,
            foregroundColor: themeProvider.isDarkMode
                ? AppTheme.onPrimaryDark
                : AppTheme.onPrimaryLight,
            centerTitle: true,
            elevation: 0,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: AppTheme.getPrimaryGradient(themeProvider.isDarkMode),
            ),
            child: Column(
              children: [
                // إحصائيات المستخدم
                _buildUserStatsCard(rewardsProvider, themeProvider),

                // قائمة المكافآت
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(top: AppTheme.mediumPadding),
                    decoration: BoxDecoration(
                      color: themeProvider.isDarkMode
                          ? AppTheme.surfaceDark
                          : Colors.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                    ),
                    child: _buildRewardsList(rewardsProvider, themeProvider),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserStatsCard(RewardsProvider provider, ThemeProvider themeProvider) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            margin: const EdgeInsets.all(AppTheme.mediumPadding),
            padding: const EdgeInsets.all(AppTheme.largePadding),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: AppTheme.largeRadius,
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                // النقاط الإجمالية
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.stars_rounded,
                      color: Colors.amber,
                      size: AppTheme.largeIconSize,
                    ),
                    const SizedBox(width: AppTheme.smallPadding),
                    Text(
                      '${provider.userStats.totalPoints}',
                      style: themeProvider.applyFontSize(
                        const TextStyle(
                          fontSize: AppTheme.headline2Size,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppTheme.smallPadding),
                    Text(
                      'نقطة',
                      style: themeProvider.applyFontSize(
                        const TextStyle(
                          fontSize: AppTheme.headline5Size,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.mediumPadding),

                // شريط التقدم للمستوى
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'المستوى ${provider.getUserLevel()}',
                          style: themeProvider.applyFontSize(
                            const TextStyle(
                              fontSize: AppTheme.bodyText1Size,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        Text(
                          '${provider.getPointsToNextLevel()} نقطة للمستوى التالي',
                          style: themeProvider.applyFontSize(
                            const TextStyle(
                              fontSize: AppTheme.bodyText2Size,
                              color: Colors.white70,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.smallPadding),
                    LinearProgressIndicator(
                      value: provider.getLevelProgress(),
                      backgroundColor: Colors.white.withValues(alpha: 0.3),
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                      minHeight: 8,
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.mediumPadding),

                // إحصائيات إضافية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      icon: Icons.local_fire_department_rounded,
                      label: 'أيام متتالية',
                      value: '${provider.userStats.currentStreak}',
                      color: Colors.orange,
                      themeProvider: themeProvider,
                    ),
                    _buildStatItem(
                      icon: Icons.calendar_today_rounded,
                      label: 'إجمالي الزيارات',
                      value: '${provider.userStats.totalVisits}',
                      color: Colors.blue,
                      themeProvider: themeProvider,
                    ),
                    _buildStatItem(
                      icon: Icons.emoji_events_rounded,
                      label: 'أطول سلسلة',
                      value: '${provider.userStats.longestStreak}',
                      color: Colors.purple,
                      themeProvider: themeProvider,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required ThemeProvider themeProvider,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppTheme.smallPadding),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: AppTheme.smallRadius,
          ),
          child: Icon(
            icon,
            color: color,
            size: AppTheme.mediumIconSize,
          ),
        ),
        const SizedBox(height: AppTheme.extraSmallPadding),
        Text(
          value,
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.headline6Size,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        Text(
          label,
          style: themeProvider.applyFontSize(
            const TextStyle(
              fontSize: AppTheme.captionSize,
              color: Colors.white70,
            ),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRewardsList(RewardsProvider provider, ThemeProvider themeProvider) {
    if (provider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (provider.recentRewards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: AppTheme.extraLargeIconSize * 2,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: AppTheme.mediumPadding),
            Text(
              'لا توجد مكافآت بعد',
              style: themeProvider.applyFontSize(
                TextStyle(
                  fontSize: AppTheme.headline5Size,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
            const SizedBox(height: AppTheme.smallPadding),
            Text(
              'استمر في استخدام التطبيق لكسب المكافآت!',
              style: themeProvider.applyFontSize(
                TextStyle(
                  fontSize: AppTheme.bodyText2Size,
                  color: Colors.grey.shade500,
                ),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.mediumPadding),
      itemCount: provider.recentRewards.length,
      itemBuilder: (context, index) {
        final reward = provider.recentRewards[index];
        return _buildRewardCard(reward, themeProvider, index);
      },
    );
  }

  Widget _buildRewardCard(Reward reward, ThemeProvider themeProvider, int index) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.only(bottom: AppTheme.mediumPadding),
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: AppTheme.mediumRadius,
              ),
              color: themeProvider.isDarkMode ? AppTheme.surfaceDark : Colors.white,
              child: Container(
                padding: const EdgeInsets.all(AppTheme.mediumPadding),
                decoration: BoxDecoration(
                  borderRadius: AppTheme.mediumRadius,
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [
                      _getRewardColor(reward.color).withValues(alpha: 0.1),
                      _getRewardColor(reward.color).withValues(alpha: 0.05),
                    ],
                  ),
                ),
                child: Row(
                  children: [
                    // أيقونة المكافأة
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: _getRewardColor(reward.color).withValues(alpha: 0.2),
                        borderRadius: AppTheme.mediumRadius,
                      ),
                      child: Center(
                        child: Text(
                          reward.icon,
                          style: const TextStyle(fontSize: 30),
                        ),
                      ),
                    ),

                    const SizedBox(width: AppTheme.mediumPadding),

                    // تفاصيل المكافأة
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            reward.title,
                            style: themeProvider.applyFontSize(
                              TextStyle(
                                fontSize: AppTheme.headline6Size,
                                fontWeight: FontWeight.bold,
                                color: themeProvider.isDarkMode
                                    ? AppTheme.onSurfaceDark
                                    : AppTheme.onSurfaceLight,
                              ),
                            ),
                          ),
                          const SizedBox(height: AppTheme.extraSmallPadding),
                          Text(
                            reward.description,
                            style: themeProvider.applyFontSize(
                              TextStyle(
                                fontSize: AppTheme.bodyText2Size,
                                color: themeProvider.isDarkMode
                                    ? AppTheme.onSurfaceDark.withValues(alpha: 0.7)
                                    : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                              ),
                            ),
                          ),
                          const SizedBox(height: AppTheme.smallPadding),
                          Row(
                            children: [
                              Icon(
                                Icons.access_time,
                                size: AppTheme.smallIconSize,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: AppTheme.extraSmallPadding),
                              Text(
                                _formatDate(reward.earnedAt),
                                style: themeProvider.applyFontSize(
                                  TextStyle(
                                    fontSize: AppTheme.captionSize,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // النقاط
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.smallPadding,
                        vertical: AppTheme.extraSmallPadding,
                      ),
                      decoration: BoxDecoration(
                        color: _getRewardColor(reward.color),
                        borderRadius: AppTheme.smallRadius,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.stars,
                            color: Colors.white,
                            size: AppTheme.smallIconSize,
                          ),
                          const SizedBox(width: AppTheme.extraSmallPadding),
                          Text(
                            '+${reward.points}',
                            style: themeProvider.applyFontSize(
                              const TextStyle(
                                fontSize: AppTheme.bodyText2Size,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getRewardColor(String colorHex) {
    return Color(int.parse(colorHex.substring(1, 7), radix: 16) + 0xFF000000);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
