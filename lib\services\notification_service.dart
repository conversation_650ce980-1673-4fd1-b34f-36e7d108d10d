import 'package:flutter/material.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // قائمة الإشعارات
  final List<AppNotification> _notifications = [];
  
  // مستمعين للتغييرات
  final List<VoidCallback> _listeners = [];

  List<AppNotification> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  bool get hasUnread => unreadCount > 0;

  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  // إضافة إشعار جديد
  void addNotification({
    required String title,
    required String message,
    NotificationType type = NotificationType.info,
    String? actionText,
    VoidCallback? onAction,
  }) {
    final notification = AppNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      timestamp: DateTime.now(),
      actionText: actionText,
      onAction: onAction,
    );

    _notifications.insert(0, notification);
    
    // الاحتفاظ بآخر 50 إشعار فقط
    if (_notifications.length > 50) {
      _notifications.removeRange(50, _notifications.length);
    }

    _notifyListeners();
  }

  // قراءة إشعار
  void markAsRead(String id) {
    final index = _notifications.indexWhere((n) => n.id == id);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _notifyListeners();
    }
  }

  // قراءة جميع الإشعارات
  void markAllAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    _notifyListeners();
  }

  // حذف إشعار
  void deleteNotification(String id) {
    _notifications.removeWhere((n) => n.id == id);
    _notifyListeners();
  }

  // مسح جميع الإشعارات
  void clearAll() {
    _notifications.clear();
    _notifyListeners();
  }

  // إشعارات تلقائية للتطبيق
  void showWelcomeNotification() {
    addNotification(
      title: 'مرحباً بك في تطبيق السيرة النبوية',
      message: 'استكشف حياة النبي محمد ﷺ والأحاديث الشريفة وسير الصحابة',
      type: NotificationType.success,
    );
  }

  void showDailyHadithNotification() {
    addNotification(
      title: 'حديث اليوم',
      message: 'اكتشف حديثاً جديداً من الأحاديث النبوية الشريفة',
      type: NotificationType.info,
      actionText: 'عرض',
    );
  }

  void showNewContentNotification(String content) {
    addNotification(
      title: 'محتوى جديد',
      message: 'تم إضافة $content جديد للتطبيق',
      type: NotificationType.info,
    );
  }
}

enum NotificationType {
  info,
  success,
  warning,
  error,
}

class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final String? actionText;
  final VoidCallback? onAction;

  const AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.actionText,
    this.onAction,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    String? actionText,
    VoidCallback? onAction,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      actionText: actionText ?? this.actionText,
      onAction: onAction ?? this.onAction,
    );
  }

  IconData get icon {
    switch (type) {
      case NotificationType.info:
        return Icons.info_outline;
      case NotificationType.success:
        return Icons.check_circle_outline;
      case NotificationType.warning:
        return Icons.warning_amber_outlined;
      case NotificationType.error:
        return Icons.error_outline;
    }
  }

  Color get color {
    switch (type) {
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    }
  }
}
