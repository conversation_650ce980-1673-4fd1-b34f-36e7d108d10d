class Reward {
  final String id;
  final String title;
  final String description;
  final int points;
  final RewardType type;
  final DateTime earnedAt;
  final String icon;
  final String color;

  const Reward({
    required this.id,
    required this.title,
    required this.description,
    required this.points,
    required this.type,
    required this.earnedAt,
    required this.icon,
    required this.color,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'points': points,
      'type': type.toString(),
      'earnedAt': earnedAt.toIso8601String(),
      'icon': icon,
      'color': color,
    };
  }

  factory Reward.fromJson(Map<String, dynamic> json) {
    return Reward(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      points: json['points'],
      type: RewardType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => RewardType.daily,
      ),
      earnedAt: DateTime.parse(json['earnedAt']),
      icon: json['icon'],
      color: json['color'],
    );
  }
}

enum RewardType {
  firstTime,    // أول مرة
  daily,        // يومي
  weekly,       // أسبوعي
  monthly,      // شهري
  streak,       // متتالي
  special,      // خاص
}

class UserStats {
  final int totalPoints;
  final int currentStreak;
  final int longestStreak;
  final DateTime lastVisit;
  final DateTime firstVisit;
  final int totalVisits;
  final List<String> earnedRewards;

  const UserStats({
    required this.totalPoints,
    required this.currentStreak,
    required this.longestStreak,
    required this.lastVisit,
    required this.firstVisit,
    required this.totalVisits,
    required this.earnedRewards,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalPoints': totalPoints,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'lastVisit': lastVisit.toIso8601String(),
      'firstVisit': firstVisit.toIso8601String(),
      'totalVisits': totalVisits,
      'earnedRewards': earnedRewards,
    };
  }

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalPoints: json['totalPoints'] ?? 0,
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      lastVisit: DateTime.parse(json['lastVisit']),
      firstVisit: DateTime.parse(json['firstVisit']),
      totalVisits: json['totalVisits'] ?? 0,
      earnedRewards: List<String>.from(json['earnedRewards'] ?? []),
    );
  }

  UserStats copyWith({
    int? totalPoints,
    int? currentStreak,
    int? longestStreak,
    DateTime? lastVisit,
    DateTime? firstVisit,
    int? totalVisits,
    List<String>? earnedRewards,
  }) {
    return UserStats(
      totalPoints: totalPoints ?? this.totalPoints,
      currentStreak: currentStreak ?? this.currentStreak,
      longestStreak: longestStreak ?? this.longestStreak,
      lastVisit: lastVisit ?? this.lastVisit,
      firstVisit: firstVisit ?? this.firstVisit,
      totalVisits: totalVisits ?? this.totalVisits,
      earnedRewards: earnedRewards ?? this.earnedRewards,
    );
  }
}

class RewardConstants {
  // نقاط المكافآت
  static const int firstTimePoints = 10;
  static const int dailyPoints = 15;
  static const int weeklyStreakPoints = 25;
  static const int monthlyStreakPoints = 50;
  static const int specialPoints = 100;

  // العبارات التحفيزية
  static const List<String> motivationalQuotes = [
    "بارك الله فيك! استمر في طلب العلم",
    "ما شاء الله! إنجاز رائع",
    "أحسنت! العلم نور",
    "ممتاز! واصل المسيرة",
    "رائع! بارك الله في جهودك",
    "عظيم! العلم خير زاد",
    "مبارك! استمر في التعلم",
    "جميل! العلم عبادة",
    "ممتاز! وفقك الله",
    "رائع! زادك الله علماً",
  ];

  // الأيقونات
  static const Map<RewardType, String> rewardIcons = {
    RewardType.firstTime: '🌟',
    RewardType.daily: '⭐',
    RewardType.weekly: '🏆',
    RewardType.monthly: '👑',
    RewardType.streak: '🔥',
    RewardType.special: '💎',
  };

  // الألوان
  static const Map<RewardType, String> rewardColors = {
    RewardType.firstTime: '#FFD700',  // ذهبي
    RewardType.daily: '#4CAF50',      // أخضر
    RewardType.weekly: '#FF9800',     // برتقالي
    RewardType.monthly: '#9C27B0',    // بنفسجي
    RewardType.streak: '#F44336',     // أحمر
    RewardType.special: '#2196F3',    // أزرق
  };
}
