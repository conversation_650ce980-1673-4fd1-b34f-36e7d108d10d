import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../models/reward.dart';
import '../theme/app_theme.dart';

class RewardPopup extends StatefulWidget {
  final Reward reward;
  final VoidCallback onClose;

  const RewardPopup({
    super.key,
    required this.reward,
    required this.onClose,
  });

  @override
  State<RewardPopup> createState() => _RewardPopupState();
}

class _RewardPopupState extends State<RewardPopup>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  late AnimationController _particleController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _particleAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeOut,
    ));

    // بدء الأنيميشن
    _scaleController.forward();
    _rotationController.repeat();
    _particleController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _rotationController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Material(
          color: Colors.black.withValues(alpha: 0.7),
          child: Center(
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Container(
                    margin: const EdgeInsets.all(AppTheme.largePadding),
                    padding: const EdgeInsets.all(AppTheme.largePadding),
                    decoration: BoxDecoration(
                      color: themeProvider.isDarkMode
                          ? AppTheme.surfaceDark
                          : Colors.white,
                      borderRadius: AppTheme.extraLargeRadius,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // أيقونة المكافأة مع الأنيميشن
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            // جزيئات متحركة
                            AnimatedBuilder(
                              animation: _particleAnimation,
                              builder: (context, child) {
                                return CustomPaint(
                                  size: const Size(120, 120),
                                  painter: ParticlePainter(
                                    progress: _particleAnimation.value,
                                    color: _getRewardColor(widget.reward.color),
                                  ),
                                );
                              },
                            ),

                            // الأيقونة الدوارة
                            AnimatedBuilder(
                              animation: _rotationAnimation,
                              builder: (context, child) {
                                return Transform.rotate(
                                  angle: _rotationAnimation.value * 2 * 3.14159,
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      color: _getRewardColor(widget.reward.color),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: _getRewardColor(widget.reward.color)
                                              .withValues(alpha: 0.5),
                                          blurRadius: 15,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                    child: Center(
                                      child: Text(
                                        widget.reward.icon,
                                        style: const TextStyle(fontSize: 40),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),

                        const SizedBox(height: AppTheme.largePadding),

                        // عنوان المكافأة
                        Text(
                          widget.reward.title,
                          style: themeProvider.applyFontSize(
                            TextStyle(
                              fontSize: AppTheme.headline4Size,
                              fontWeight: FontWeight.bold,
                              color: themeProvider.isDarkMode
                                  ? AppTheme.onSurfaceDark
                                  : AppTheme.onSurfaceLight,
                            ),
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: AppTheme.mediumPadding),

                        // وصف المكافأة
                        Text(
                          widget.reward.description,
                          style: themeProvider.applyFontSize(
                            TextStyle(
                              fontSize: AppTheme.bodyText1Size,
                              color: themeProvider.isDarkMode
                                  ? AppTheme.onSurfaceDark.withValues(alpha: 0.8)
                                  : AppTheme.onSurfaceLight.withValues(alpha: 0.8),
                              height: 1.5,
                            ),
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: AppTheme.largePadding),

                        // النقاط المكتسبة
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppTheme.largePadding,
                            vertical: AppTheme.mediumPadding,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                _getRewardColor(widget.reward.color),
                                _getRewardColor(widget.reward.color).withValues(alpha: 0.8),
                              ],
                            ),
                            borderRadius: AppTheme.largeRadius,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.stars_rounded,
                                color: Colors.white,
                                size: AppTheme.mediumIconSize,
                              ),
                              const SizedBox(width: AppTheme.smallPadding),
                              Text(
                                '+${widget.reward.points}',
                                style: themeProvider.applyFontSize(
                                  const TextStyle(
                                    fontSize: AppTheme.headline5Size,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: AppTheme.smallPadding),
                              Text(
                                'نقطة',
                                style: themeProvider.applyFontSize(
                                  const TextStyle(
                                    fontSize: AppTheme.bodyText1Size,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: AppTheme.largePadding),

                        // زر الإغلاق
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: widget.onClose,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeProvider.isDarkMode
                                  ? AppTheme.primaryDark
                                  : AppTheme.primaryLight,
                              foregroundColor: themeProvider.isDarkMode
                                  ? AppTheme.onPrimaryDark
                                  : AppTheme.onPrimaryLight,
                              padding: const EdgeInsets.symmetric(
                                vertical: AppTheme.mediumPadding,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: AppTheme.mediumRadius,
                              ),
                            ),
                            child: Text(
                              'رائع! 🎉',
                              style: themeProvider.applyFontSize(
                                const TextStyle(
                                  fontSize: AppTheme.bodyText1Size,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Color _getRewardColor(String colorHex) {
    return Color(int.parse(colorHex.substring(1, 7), radix: 16) + 0xFF000000);
  }
}

// رسام الجزيئات المتحركة
class ParticlePainter extends CustomPainter {
  final double progress;
  final Color color;

  ParticlePainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.6)
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // رسم 8 جزيئات حول الدائرة
    for (int i = 0; i < 8; i++) {
      final angle = (i * 2 * 3.14159) / 8;
      final distance = radius * progress;
      final particleX = center.dx + distance * 0.8 * (1 + 0.3 * progress) *
          (1 + 0.2 * (i % 2 == 0 ? 1 : -1)) * // تنويع المسافة
          (0.5 + 0.5 * progress) * // تأثير التلاشي
          (1 + 0.1 * (i % 3)) * // تنويع إضافي
          (0.8 + 0.4 * progress) * // تأثير التوسع
          (1 + 0.1 * (angle / 3.14159)); // استخدام الزاوية
      final particleY = center.dy + distance * 0.8 * (1 + 0.3 * progress) *
          (1 + 0.2 * (i % 2 == 0 ? 1 : -1)) * // تنويع المسافة
          (0.5 + 0.5 * progress) * // تأثير التلاشي
          (1 + 0.1 * (i % 3)) * // تنويع إضافي
          (0.8 + 0.4 * progress) * // تأثير التوسع
          (1 + 0.1 * (angle / 3.14159)); // استخدام الزاوية

      final particleSize = 4.0 * (1 - progress) + 2.0;

      canvas.drawCircle(
        Offset(
          center.dx + (particleX - center.dx) * (0.5 + 0.5 * progress),
          center.dy + (particleY - center.dy) * (0.5 + 0.5 * progress),
        ),
        particleSize,
        paint..color = color.withValues(alpha: 0.8 * (1 - progress)),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
