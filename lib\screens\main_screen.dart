import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../theme/app_theme.dart';
import 'home_screen.dart';
import 'seerah_screen.dart';
import 'hadith_screen.dart';
import 'search_screen.dart';
import 'favorites_screen.dart';
import 'settings_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  // قائمة الشاشات (بدون الإعدادات)
  final List<Widget> _screens = [
    const HomeScreen(),
    const SeerahScreen(),
    const HadithScreen(),
    const SearchScreen(),
    const FavoritesScreen(),
  ];

  // قائمة عناوين الشاشات (بدون الإعدادات)
  final List<String> _titles = [
    'الرئيسية',
    'السيرة النبوية',
    'الأحاديث النبوية',
    'البحث',
    'المفضلة',
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              _titles[_currentIndex],
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            centerTitle: true,
            elevation: 0,
            iconTheme: IconThemeData(
              color: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
            ),
          ),
          drawer: _buildDrawer(context, themeProvider, favoritesProvider),
          body: IndexedStack(
            index: _currentIndex,
            children: _screens,
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
            selectedItemColor: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            unselectedItemColor: themeProvider.isDarkMode
                ? AppTheme.onSurfaceDark.withValues(alpha: 0.6)
                : AppTheme.onSurfaceLight.withValues(alpha: 0.6),
            selectedLabelStyle: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: (themeProvider.fontSize - 2).clamp(10.0, 20.0),
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: (themeProvider.fontSize - 2).clamp(10.0, 20.0),
            ),
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home_rounded),
                activeIcon: Icon(Icons.home),
                label: 'الرئيسية',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.book_rounded),
                activeIcon: Icon(Icons.book),
                label: 'السيرة',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.format_quote_rounded),
                activeIcon: Icon(Icons.format_quote),
                label: 'الأحاديث',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.search_rounded),
                activeIcon: Icon(Icons.search),
                label: 'البحث',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.favorite_rounded),
                activeIcon: Icon(Icons.favorite),
                label: 'المفضلة',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDrawer(BuildContext context, ThemeProvider themeProvider, FavoritesProvider favoritesProvider) {
    return Drawer(
      backgroundColor: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
      child: SafeArea(
        child: Column(
          children: [
          // Header
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: themeProvider.isDarkMode
                    ? [AppTheme.primaryDark, AppTheme.primaryDark.withValues(alpha: 0.8)]
                    : [AppTheme.primaryLight, AppTheme.primaryLight.withValues(alpha: 0.8)],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CircleAvatar(
                      radius: 35,
                      backgroundColor: Colors.white.withValues(alpha: 0.2),
                      child: Icon(
                        Icons.mosque_rounded,
                        size: 40,
                        color: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'تطبيق السيرة النبوية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'والأحاديث الشريفة',
                      style: TextStyle(
                        fontSize: 14,
                        color: (themeProvider.isDarkMode ? AppTheme.onPrimaryDark : AppTheme.onPrimaryLight).withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8),
              children: [
                _buildDrawerItem(
                  context,
                  themeProvider,
                  icon: Icons.settings_rounded,
                  title: 'الإعدادات',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const SettingsScreen()),
                    );
                  },
                ),
                _buildDrawerItem(
                  context,
                  themeProvider,
                  icon: Icons.info_rounded,
                  title: 'حول التطبيق',
                  onTap: () {
                    Navigator.pop(context);
                    _showAboutDialog(context, themeProvider);
                  },
                ),
                _buildDrawerItem(
                  context,
                  themeProvider,
                  icon: Icons.share_rounded,
                  title: 'مشاركة التطبيق',
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: إضافة وظيفة المشاركة
                  },
                ),
                const Divider(),
                _buildDrawerItem(
                  context,
                  themeProvider,
                  icon: themeProvider.isDarkMode ? Icons.light_mode_rounded : Icons.dark_mode_rounded,
                  title: themeProvider.isDarkMode ? 'الوضع الفاتح' : 'الوضع الليلي',
                  onTap: () {
                    themeProvider.toggleTheme();
                  },
                ),
                _buildDrawerItem(
                  context,
                  themeProvider,
                  icon: Icons.contrast_rounded,
                  title: 'التباين العالي',
                  trailing: Switch(
                    value: themeProvider.isHighContrast,
                    onChanged: (value) {
                      themeProvider.toggleHighContrast();
                    },
                    activeColor: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                  ),
                  onTap: () {
                    themeProvider.toggleHighContrast();
                  },
                ),
              ],
            ),
          ),

          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  'إحصائيات المفضلة',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      themeProvider,
                      'السيرة',
                      favoritesProvider.seerahCount.toString(),
                      Icons.book_rounded,
                    ),
                    _buildStatItem(
                      themeProvider,
                      'الأحاديث',
                      favoritesProvider.hadithCount.toString(),
                      Icons.format_quote_rounded,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'المطور: وائل شايبي 2025',
                  style: TextStyle(
                    fontSize: 12,
                    color: (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context,
    ThemeProvider themeProvider, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.transparent,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: themeProvider.fontSize,
            fontWeight: FontWeight.w600, // جعل النص أكثر وضوحاً
            color: themeProvider.isDarkMode ? Colors.white : AppTheme.onSurfaceLight, // أبيض في الوضع الليلي
          ),
        ),
        trailing: trailing,
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        hoverColor: (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1),
        splashColor: (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.2),
      ),
    );
  }

  Widget _buildStatItem(ThemeProvider themeProvider, String label, String count, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
        ),
        const SizedBox(height: 4),
        Text(
          count,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  void _showAboutDialog(BuildContext context, ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        title: Text(
          'حول التطبيق',
          style: TextStyle(
            color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
          ),
        ),
        content: Text(
          'تطبيق السيرة النبوية والأحاديث الشريفة\n\nيحتوي على مجموعة شاملة من أحداث السيرة النبوية والأحاديث الصحيحة مع إمكانية البحث والمفضلة.\n\nالمطور: وائل شايبي 2025',
          style: TextStyle(
            color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'موافق',
              style: TextStyle(
                color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
