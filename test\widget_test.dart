// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:seerah_app_new/main.dart';

void main() {
  testWidgets('Seerah app with Provider test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SeerahApp());

    // Wait for initialization to complete
    await tester.pumpAndSettle();

    // Verify that the main screen is displayed.
    expect(find.text('الرئيسية'), findsOneWidget);

    // Verify that the bottom navigation bar is displayed.
    expect(find.byType(BottomNavigationBar), findsOneWidget);

    // Verify that navigation items are present.
    expect(find.text('السيرة'), findsOneWidget);
    expect(find.text('الأحاديث'), findsOneWidget);

    // Verify that the home screen content is displayed.
    expect(find.text('سيرة النبي محمد ﷺ'), findsWidgets);
    expect(find.text('السيرة النبوية'), findsWidgets);
    expect(find.text('الأحاديث النبوية'), findsWidgets);

    // Test navigation to Seerah screen.
    await tester.tap(find.text('السيرة'));
    await tester.pumpAndSettle();
    expect(find.text('السيرة النبوية'), findsWidgets);

    // Test navigation to Hadith screen.
    await tester.tap(find.text('الأحاديث'));
    await tester.pumpAndSettle();
    expect(find.text('الأحاديث النبوية'), findsWidgets);

    // Test navigation back to home.
    await tester.tap(find.text('الرئيسية'));
    await tester.pumpAndSettle();
    expect(find.text('مرحباً بك في تطبيق السيرة النبوية'), findsOneWidget);
  });
}
