class Hadith {
  final String id;
  final String arabicText;
  final String translation;
  final String narrator;
  final String source;
  final String category;
  final String theme;
  final int number;
  final bool isAuthentic;
  final bool isAvailable;
  final List<String> keywords;

  const Hadith({
    required this.id,
    required this.arabicText,
    required this.translation,
    required this.narrator,
    required this.source,
    required this.category,
    required this.theme,
    required this.number,
    this.isAuthentic = true,
    this.isAvailable = true,
    this.keywords = const [],
  });

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'translation': translation,
      'narrator': narrator,
      'source': source,
      'category': category,
      'theme': theme,
      'number': number,
      'isAuthentic': isAuthentic,
      'isAvailable': isAvailable,
      'keywords': keywords,
    };
  }

  // إنشاء من JSON
  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id'] ?? '',
      arabicText: json['arabicText'] ?? '',
      translation: json['translation'] ?? '',
      narrator: json['narrator'] ?? '',
      source: json['source'] ?? '',
      category: json['category'] ?? '',
      theme: json['theme'] ?? '',
      number: json['number'] ?? 0,
      isAuthentic: json['isAuthentic'] ?? true,
      isAvailable: json['isAvailable'] ?? true,
      keywords: List<String>.from(json['keywords'] ?? []),
    );
  }

  // نسخ مع تعديل
  Hadith copyWith({
    String? id,
    String? arabicText,
    String? translation,
    String? narrator,
    String? source,
    String? category,
    String? theme,
    int? number,
    bool? isAuthentic,
    bool? isAvailable,
    List<String>? keywords,
  }) {
    return Hadith(
      id: id ?? this.id,
      arabicText: arabicText ?? this.arabicText,
      translation: translation ?? this.translation,
      narrator: narrator ?? this.narrator,
      source: source ?? this.source,
      category: category ?? this.category,
      theme: theme ?? this.theme,
      number: number ?? this.number,
      isAuthentic: isAuthentic ?? this.isAuthentic,
      isAvailable: isAvailable ?? this.isAvailable,
      keywords: keywords ?? this.keywords,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Hadith && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Hadith(id: $id, number: $number, category: $category)';
  }
}
