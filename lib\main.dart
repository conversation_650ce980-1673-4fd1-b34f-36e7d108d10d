import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'screens/main_screen.dart';
import 'providers/app_provider.dart';
import 'providers/seerah_provider.dart';
import 'providers/hadith_provider.dart';
import 'providers/companions_provider.dart';
import 'providers/theme_provider.dart' as theme_provider;
import 'providers/favorites_provider.dart';
import 'providers/rewards_provider.dart';
import 'theme/app_theme.dart';

void main() {
  runApp(const SeerahApp());
}

class SeerahApp extends StatelessWidget {
  const SeerahApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => theme_provider.ThemeProvider()),
        ChangeNotifierProvider(create: (_) => FavoritesProvider()),
        ChangeNotifierProvider(create: (_) => RewardsProvider()),
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => SeerahProvider()),
        ChangeNotifierProvider(create: (_) => HadithProvider()),
        ChangeNotifierProvider(create: (_) => CompanionsProvider()),
      ],
      child: Consumer<theme_provider.ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'سيرة النبي محمد ﷺ',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode == theme_provider.ThemeMode.system
                ? ThemeMode.system
                : themeProvider.themeMode == theme_provider.ThemeMode.dark
                    ? ThemeMode.dark
                    : ThemeMode.light,
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final seerahProvider = Provider.of<SeerahProvider>(context, listen: false);
    final hadithProvider = Provider.of<HadithProvider>(context, listen: false);
    final companionsProvider = Provider.of<CompanionsProvider>(context, listen: false);
    final themeProvider = Provider.of<theme_provider.ThemeProvider>(context, listen: false);
    final rewardsProvider = Provider.of<RewardsProvider>(context, listen: false);

    try {
      // تحميل الإعدادات
      await Future.wait([
        appProvider.loadSettings(),
        themeProvider.loadSettings(),
        rewardsProvider.loadUserStats(),
      ]);

      // تحميل البيانات
      await Future.wait([
        seerahProvider.loadInitialData(),
        hadithProvider.loadInitialData(),
        companionsProvider.loadInitialData(),
      ]);

      // تحديث الإحصائيات
      appProvider.updateStatistics(
        seerahEvents: seerahProvider.allEvents.length,
        hadiths: hadithProvider.allHadiths.length,
      );

      // فحص وإضافة المكافآت اليومية
      await rewardsProvider.checkAndAddDailyReward();

    } catch (e) {
      appProvider.setError('خطأ في تحميل التطبيق: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        if (appProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تحميل التطبيق...'),
                ],
              ),
            ),
          );
        }

        return const MainScreen();
      },
    );
  }
}


