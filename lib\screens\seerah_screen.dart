import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../models/seerah_event.dart';
import '../theme/app_theme.dart';

class SeerahScreen extends StatefulWidget {
  const SeerahScreen({super.key});

  @override
  State<SeerahScreen> createState() => _SeerahScreenState();
}

class _SeerahScreenState extends State<SeerahScreen> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<SeerahProvider, FavoritesProvider, ThemeProvider>(
      builder: (context, seerahProvider, favoritesProvider, themeProvider, child) {
        // تنظيف البحث العام للتأكد من عرض جميع الأحداث
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (seerahProvider.searchQuery.isNotEmpty) {
            seerahProvider.updateSearch('');
          }
        });
        return Scaffold(
          body: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF102c23), // أخضر داكن جداً
                  Color(0xFF183f33), // أخضر داكن
                  Color(0xFF123127), // أخضر متوسط
                  Color(0xFF113026), // أخضر فاتح نسبياً
                ],
                stops: [0.0, 0.3, 0.7, 1.0],
              ),
            ),
            child: seerahProvider.isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          color: Colors.white,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'جاري تحميل أحداث السيرة...',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  )
                : _buildEventsList(seerahProvider, favoritesProvider, themeProvider),
          ),
        );
      },
    );
  }

  Widget _buildEventsList(SeerahProvider seerahProvider, FavoritesProvider favoritesProvider, ThemeProvider themeProvider) {
    // عرض جميع الأحداث
    final allEvents = seerahProvider.events;

    if (allEvents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.book_rounded,
              size: 80,
              color: (themeProvider.isDarkMode
                      ? AppTheme.onPrimaryDark
                      : AppTheme.onPrimaryLight)
                  .withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد أحداث متاحة حالياً',
              style: TextStyle(
                color: themeProvider.isDarkMode
                    ? AppTheme.onPrimaryDark
                    : AppTheme.onPrimaryLight,
                fontSize: 18,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: allEvents.length + 1, // +1 للإحصائيات
      itemBuilder: (context, index) {
        if (index == allEvents.length) {
          // إضافة كارت الإحصائيات في النهاية
          return _buildStatisticsCard(seerahProvider, themeProvider);
        }

        final event = allEvents[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildEventCard(event, favoritesProvider, context, themeProvider),
        );
      },
    );
  }

  Widget _buildEventCard(SeerahEvent event, FavoritesProvider favoritesProvider, BuildContext context, ThemeProvider themeProvider) {
    IconData iconData;
    switch (event.iconName) {
      case 'star':
        iconData = Icons.star_rounded;
        break;
      case 'heart_broken':
        iconData = Icons.heart_broken_rounded;
        break;
      case 'child_care':
        iconData = Icons.child_care_rounded;
        break;
      case 'healing':
        iconData = Icons.healing_rounded;
        break;
      case 'elderly':
        iconData = Icons.elderly_rounded;
        break;
      case 'family_restroom':
        iconData = Icons.family_restroom_rounded;
        break;
      case 'travel_explore':
        iconData = Icons.travel_explore_rounded;
        break;
      case 'security':
        iconData = Icons.security_rounded;
        break;
      case 'balance':
        iconData = Icons.balance_rounded;
        break;
      case 'business':
        iconData = Icons.business_rounded;
        break;
      case 'local_shipping':
        iconData = Icons.local_shipping_rounded;
        break;
      case 'favorite':
        iconData = Icons.favorite_rounded;
        break;
      case 'architecture':
        iconData = Icons.architecture_rounded;
        break;
      case 'self_improvement':
        iconData = Icons.self_improvement_rounded;
        break;
      case 'auto_awesome':
        iconData = Icons.auto_awesome_rounded;
        break;
      case 'person_add':
        iconData = Icons.person_add_rounded;
        break;
      case 'child_friendly':
        iconData = Icons.child_friendly_rounded;
        break;
      case 'visibility_off':
        iconData = Icons.visibility_off_rounded;
        break;
      case 'campaign':
        iconData = Icons.campaign_rounded;
        break;
      case 'warning':
        iconData = Icons.warning_rounded;
        break;
      case 'flight_takeoff':
        iconData = Icons.flight_takeoff_rounded;
        break;
      case 'shield':
        iconData = Icons.shield_rounded;
        break;
      case 'gavel':
        iconData = Icons.gavel_rounded;
        break;
      case 'block':
        iconData = Icons.block_rounded;
        break;
      case 'sentiment_very_dissatisfied':
        iconData = Icons.sentiment_very_dissatisfied_rounded;
        break;
      case 'directions_walk':
        iconData = Icons.directions_walk_rounded;
        break;
      case 'flight':
        iconData = Icons.flight_rounded;
        break;
      default:
        iconData = Icons.event_rounded;
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: event.isAvailable
              ? (themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight)
              : (themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
          border: themeProvider.isHighContrast
              ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
              : null,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: event.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1)
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    iconData,
                    size: 32,
                    color: event.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title,
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: event.isAvailable
                                ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight)
                                : Colors.grey.shade600,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        event.subtitle,
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 14,
                            color: event.isAvailable
                                ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7)
                                : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () {
                        favoritesProvider.toggleSeerahFavorite(event);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              favoritesProvider.isSeerahFavorite(event.id)
                                  ? 'تم إضافة الحدث للمفضلة'
                                  : 'تم إزالة الحدث من المفضلة',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      icon: Icon(
                        favoritesProvider.isSeerahFavorite(event.id)
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: favoritesProvider.isSeerahFavorite(event.id)
                            ? Colors.red
                            : (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
                      ),
                      tooltip: favoritesProvider.isSeerahFavorite(event.id)
                          ? 'إزالة من المفضلة'
                          : 'إضافة للمفضلة',
                    ),
                    Icon(
                      event.isAvailable ? Icons.arrow_forward_ios : Icons.schedule,
                      color: event.isAvailable
                          ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight)
                          : Colors.grey.shade400,
                      size: 20,
                    ),
                  ],
                ),
              ],
            ),
            if (event.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                event.description,
                style: themeProvider.applyFontSize(
                  TextStyle(
                    fontSize: 13,
                    color: event.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.8)
                        : Colors.grey.shade500,
                    height: 1.4,
                  ),
                ),
              ),
            ],
            if (event.date.isNotEmpty || event.location.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (event.date.isNotEmpty) ...[
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      event.date,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                  if (event.date.isNotEmpty && event.location.isNotEmpty)
                    const SizedBox(width: 16),
                  if (event.location.isNotEmpty) ...[
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      event.location,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(SeerahProvider provider, ThemeProvider themeProvider) {
    final categoryCounts = provider.categoryCounts;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
          border: themeProvider.isHighContrast
              ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
              : null,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.analytics_rounded,
              size: 40,
              color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            ),
            const SizedBox(height: 12),
            Text(
              'إحصائيات السيرة النبوية',
              style: themeProvider.applyFontSize(
                TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ...categoryCounts.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    entry.key,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                      ),
                    ),
                  ),
                  Text(
                    '${entry.value} أحداث',
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}
