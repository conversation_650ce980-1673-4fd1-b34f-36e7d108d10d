import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../models/seerah_event.dart';
import '../theme/app_theme.dart';
import 'filter_screen.dart';

class SeerahScreen extends StatefulWidget {
  const SeerahScreen({super.key});

  @override
  State<SeerahScreen> createState() => _SeerahScreenState();
}

class _SeerahScreenState extends State<SeerahScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<SeerahProvider, FavoritesProvider, ThemeProvider>(
      builder: (context, seerahProvider, favoritesProvider, themeProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'السيرة النبوية',
              style: themeProvider.applyFontSize(
                Theme.of(context).textTheme.titleLarge!,
              ),
            ),
            backgroundColor: themeProvider.isDarkMode
                ? AppTheme.primaryDark
                : AppTheme.primaryLight,
            foregroundColor: themeProvider.isDarkMode
                ? AppTheme.onPrimaryDark
                : AppTheme.onPrimaryLight,
            centerTitle: true,
            elevation: 0,
            actions: [
              IconButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const FilterScreen(type: 'seerah'),
                    ),
                  );
                },
                icon: const Icon(Icons.filter_list_rounded),
                tooltip: 'فلترة النتائج',
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: themeProvider.isDarkMode
                    ? [AppTheme.primaryDark, AppTheme.primaryVariantDark]
                    : [AppTheme.primaryLight, AppTheme.primaryVariantLight],
              ),
            ),
            child: Column(
              children: [
                // Header Section
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const SizedBox(height: 20),

                      // أيقونة القسم
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Icon(
                          Icons.book_rounded,
                          size: 80,
                          color: themeProvider.isDarkMode
                              ? AppTheme.onPrimaryDark
                              : AppTheme.onPrimaryLight,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // عنوان القسم
                      Text(
                        'السيرة النبوية الشريفة',
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: themeProvider.isDarkMode
                                ? AppTheme.onPrimaryDark
                                : AppTheme.onPrimaryLight,
                          ),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),

                      // وصف القسم مع الإحصائيات
                      Text(
                        'تعرف على ${seerahProvider.events.length} حدث مهم من حياة النبي محمد ﷺ',
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 16,
                            color: (themeProvider.isDarkMode
                                    ? AppTheme.onPrimaryDark
                                    : AppTheme.onPrimaryLight)
                                .withValues(alpha: 0.7),
                          ),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),

                      // شريط البحث
                      Container(
                        decoration: BoxDecoration(
                          color: themeProvider.isDarkMode
                              ? Colors.black.withValues(alpha: 0.3)
                              : Colors.white.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(25),
                          border: themeProvider.isHighContrast
                              ? Border.all(
                                  color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                                  width: 2,
                                )
                              : null,
                        ),
                        child: TextField(
                          controller: _searchController,
                          textDirection: TextDirection.rtl,
                          textAlign: TextAlign.right,
                          style: const TextStyle(
                            color: Colors.black, // أسود واضح كما طلب المستخدم
                            fontSize: 24.0, // حجم ثابت 24 بكسل
                            fontWeight: FontWeight.w500,
                          ),
                          decoration: InputDecoration(
                            hintText: 'ابحث في أحداث السيرة...',
                            hintStyle: const TextStyle(
                              color: Colors.black54, // رمادي واضح في كلا الوضعين
                              fontSize: 24.0, // حجم ثابت 24 بكسل
                            ),
                            prefixIcon: Icon(
                              Icons.search,
                              color: Colors.black54, // رمادي واضح في كلا الوضعين
                            ),
                            suffixIcon: _searchQuery.isNotEmpty
                                ? IconButton(
                                    onPressed: () {
                                      _searchController.clear();
                                      setState(() {
                                        _searchQuery = '';
                                      });
                                    },
                                    icon: Icon(
                                      Icons.clear,
                                      color: Colors.black54, // رمادي واضح في كلا الوضعين
                                    ),
                                  )
                                : null,
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 15,
                            ),
                          ),
                          onChanged: (value) {
                            setState(() {
                              _searchQuery = value;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Content Section
                Expanded(
                  child: seerahProvider.isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                color: themeProvider.isDarkMode
                                    ? AppTheme.onPrimaryDark
                                    : AppTheme.onPrimaryLight,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'جاري تحميل أحداث السيرة...',
                                style: TextStyle(
                                  color: themeProvider.isDarkMode
                                      ? AppTheme.onPrimaryDark
                                      : AppTheme.onPrimaryLight,
                                ),
                              ),
                            ],
                          ),
                        )
                      : _buildEventsList(seerahProvider, favoritesProvider, themeProvider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEventsList(SeerahProvider seerahProvider, FavoritesProvider favoritesProvider, ThemeProvider themeProvider) {
    // تطبيق البحث
    final filteredEvents = _searchQuery.isEmpty
        ? seerahProvider.events
        : seerahProvider.events.where((event) {
            final query = _searchQuery.toLowerCase();
            return event.title.toLowerCase().contains(query) ||
                   event.subtitle.toLowerCase().contains(query) ||
                   event.description.toLowerCase().contains(query) ||
                   event.category.toLowerCase().contains(query) ||
                   event.location.toLowerCase().contains(query);
          }).toList();

    if (filteredEvents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isEmpty ? Icons.book_rounded : Icons.search_off,
              size: 80,
              color: (themeProvider.isDarkMode
                      ? AppTheme.onPrimaryDark
                      : AppTheme.onPrimaryLight)
                  .withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty
                  ? 'لا توجد أحداث متاحة حالياً'
                  : 'لا توجد نتائج للبحث',
              style: TextStyle(
                color: themeProvider.isDarkMode
                    ? AppTheme.onPrimaryDark
                    : AppTheme.onPrimaryLight,
                fontSize: 18,
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'جرب كلمات بحث مختلفة',
                style: TextStyle(
                  color: (themeProvider.isDarkMode
                          ? AppTheme.onPrimaryDark
                          : AppTheme.onPrimaryLight)
                      .withValues(alpha: 0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: filteredEvents.length + (_searchQuery.isEmpty ? 1 : 0),
      itemBuilder: (context, index) {
        if (_searchQuery.isEmpty && index == filteredEvents.length) {
          // إضافة كارت الإحصائيات في النهاية فقط عند عدم البحث
          return _buildStatisticsCard(seerahProvider, themeProvider);
        }

        final event = filteredEvents[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildEventCard(event, favoritesProvider, context, themeProvider),
        );
      },
    );
  }

  Widget _buildEventCard(SeerahEvent event, FavoritesProvider favoritesProvider, BuildContext context, ThemeProvider themeProvider) {
    IconData iconData;
    switch (event.iconName) {
      case 'star':
        iconData = Icons.star_rounded;
        break;
      case 'heart_broken':
        iconData = Icons.heart_broken_rounded;
        break;
      case 'child_care':
        iconData = Icons.child_care_rounded;
        break;
      case 'healing':
        iconData = Icons.healing_rounded;
        break;
      case 'elderly':
        iconData = Icons.elderly_rounded;
        break;
      case 'family_restroom':
        iconData = Icons.family_restroom_rounded;
        break;
      case 'travel_explore':
        iconData = Icons.travel_explore_rounded;
        break;
      case 'security':
        iconData = Icons.security_rounded;
        break;
      case 'balance':
        iconData = Icons.balance_rounded;
        break;
      case 'business':
        iconData = Icons.business_rounded;
        break;
      case 'local_shipping':
        iconData = Icons.local_shipping_rounded;
        break;
      case 'favorite':
        iconData = Icons.favorite_rounded;
        break;
      case 'architecture':
        iconData = Icons.architecture_rounded;
        break;
      case 'self_improvement':
        iconData = Icons.self_improvement_rounded;
        break;
      case 'auto_awesome':
        iconData = Icons.auto_awesome_rounded;
        break;
      case 'person_add':
        iconData = Icons.person_add_rounded;
        break;
      case 'child_friendly':
        iconData = Icons.child_friendly_rounded;
        break;
      case 'visibility_off':
        iconData = Icons.visibility_off_rounded;
        break;
      case 'campaign':
        iconData = Icons.campaign_rounded;
        break;
      case 'warning':
        iconData = Icons.warning_rounded;
        break;
      case 'flight_takeoff':
        iconData = Icons.flight_takeoff_rounded;
        break;
      case 'shield':
        iconData = Icons.shield_rounded;
        break;
      case 'gavel':
        iconData = Icons.gavel_rounded;
        break;
      case 'block':
        iconData = Icons.block_rounded;
        break;
      case 'sentiment_very_dissatisfied':
        iconData = Icons.sentiment_very_dissatisfied_rounded;
        break;
      case 'directions_walk':
        iconData = Icons.directions_walk_rounded;
        break;
      case 'flight':
        iconData = Icons.flight_rounded;
        break;
      default:
        iconData = Icons.event_rounded;
    }

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: event.isAvailable
              ? (themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight)
              : (themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
          border: themeProvider.isHighContrast
              ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
              : null,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: event.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1)
                        : Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    iconData,
                    size: 32,
                    color: event.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight)
                        : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title,
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: event.isAvailable
                                ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight)
                                : Colors.grey.shade600,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        event.subtitle,
                        style: themeProvider.applyFontSize(
                          TextStyle(
                            fontSize: 14,
                            color: event.isAvailable
                                ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7)
                                : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () {
                        favoritesProvider.toggleSeerahFavorite(event);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              favoritesProvider.isSeerahFavorite(event.id)
                                  ? 'تم إضافة الحدث للمفضلة'
                                  : 'تم إزالة الحدث من المفضلة',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                      icon: Icon(
                        favoritesProvider.isSeerahFavorite(event.id)
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: favoritesProvider.isSeerahFavorite(event.id)
                            ? Colors.red
                            : (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.7),
                      ),
                      tooltip: favoritesProvider.isSeerahFavorite(event.id)
                          ? 'إزالة من المفضلة'
                          : 'إضافة للمفضلة',
                    ),
                    Icon(
                      event.isAvailable ? Icons.arrow_forward_ios : Icons.schedule,
                      color: event.isAvailable
                          ? (themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight)
                          : Colors.grey.shade400,
                      size: 20,
                    ),
                  ],
                ),
              ],
            ),
            if (event.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                event.description,
                style: themeProvider.applyFontSize(
                  TextStyle(
                    fontSize: 13,
                    color: event.isAvailable
                        ? (themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight).withValues(alpha: 0.8)
                        : Colors.grey.shade500,
                    height: 1.4,
                  ),
                ),
              ),
            ],
            if (event.date.isNotEmpty || event.location.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  if (event.date.isNotEmpty) ...[
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      event.date,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                  if (event.date.isNotEmpty && event.location.isNotEmpty)
                    const SizedBox(width: 16),
                  if (event.location.isNotEmpty) ...[
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      event.location,
                      style: themeProvider.applyFontSize(
                        TextStyle(
                          fontSize: 12,
                          color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark.withValues(alpha: 0.7) : AppTheme.onSurfaceLight.withValues(alpha: 0.7),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard(SeerahProvider provider, ThemeProvider themeProvider) {
    final categoryCounts = provider.categoryCounts;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: themeProvider.isDarkMode ? AppTheme.surfaceDark : AppTheme.surfaceLight,
          borderRadius: BorderRadius.circular(16),
          boxShadow: themeProvider.isDarkMode ? null : AppTheme.lightShadow,
          border: themeProvider.isHighContrast
              ? Border.all(color: themeProvider.isDarkMode ? Colors.white54 : Colors.black54)
              : null,
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.analytics_rounded,
              size: 40,
              color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
            ),
            const SizedBox(height: 12),
            Text(
              'إحصائيات السيرة النبوية',
              style: themeProvider.applyFontSize(
                TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ...categoryCounts.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    entry.key,
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        color: themeProvider.isDarkMode ? AppTheme.onSurfaceDark : AppTheme.onSurfaceLight,
                      ),
                    ),
                  ),
                  Text(
                    '${entry.value} أحداث',
                    style: themeProvider.applyFontSize(
                      TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: themeProvider.isDarkMode ? AppTheme.primaryDark : AppTheme.primaryLight,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
}
