class Companion {
  final String id;
  final String name;
  final String fullName;
  final String nickname;
  final String biography;
  final String birthPlace;
  final String deathPlace;
  final String birthYear;
  final String deathYear;
  final String tribe;
  final String category;
  final String famousFor;
  final List<String> achievements;
  final List<String> virtues;
  final List<String> keywords;
  final int number;

  const Companion({
    required this.id,
    required this.name,
    required this.fullName,
    required this.nickname,
    required this.biography,
    required this.birthPlace,
    required this.deathPlace,
    required this.birthYear,
    required this.deathYear,
    required this.tribe,
    required this.category,
    required this.famousFor,
    required this.achievements,
    required this.virtues,
    required this.keywords,
    required this.number,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'fullName': fullName,
      'nickname': nickname,
      'biography': biography,
      'birthPlace': birthPlace,
      'deathPlace': deathPlace,
      'birthYear': birthYear,
      'deathYear': deathYear,
      'tribe': tribe,
      'category': category,
      'famousFor': famousFor,
      'achievements': achievements,
      'virtues': virtues,
      'keywords': keywords,
      'number': number,
    };
  }

  factory Companion.from<PERSON>son(Map<String, dynamic> json) {
    return Companion(
      id: json['id'],
      name: json['name'],
      fullName: json['fullName'],
      nickname: json['nickname'],
      biography: json['biography'],
      birthPlace: json['birthPlace'],
      deathPlace: json['deathPlace'],
      birthYear: json['birthYear'],
      deathYear: json['deathYear'],
      tribe: json['tribe'],
      category: json['category'],
      famousFor: json['famousFor'],
      achievements: List<String>.from(json['achievements']),
      virtues: List<String>.from(json['virtues']),
      keywords: List<String>.from(json['keywords']),
      number: json['number'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Companion && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
