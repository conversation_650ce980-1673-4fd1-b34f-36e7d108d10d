import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/companions_provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/theme_provider.dart';
import '../models/companion.dart';
import '../screens/companion_detail_screen.dart';
import '../theme/app_theme.dart';

class CompanionsScreen extends StatefulWidget {
  const CompanionsScreen({super.key});

  @override
  State<CompanionsScreen> createState() => _CompanionsScreenState();
}

class _CompanionsScreenState extends State<CompanionsScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تنظيف البحث العام للتأكد من عرض جميع الصحابة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<CompanionsProvider>();
      if (provider.searchQuery.isNotEmpty) {
        provider.searchCompanions('');
      }
    });

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: Theme.of(context).brightness == Brightness.dark
                ? AppTheme.primaryGradientDark
                : AppTheme.primaryGradientLight,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.arrow_back, color: Colors.white),
                        ),
                        const Expanded(
                          child: Text(
                            'الصحابة الكرام',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        const SizedBox(width: 48),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: TextField(
                        controller: _searchController,
                        onChanged: (value) {
                          context.read<CompanionsProvider>().searchCompanions(value);
                        },
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24.0, // حجم ثابت 24 بكسل
                          fontWeight: FontWeight.w500,
                        ),
                        decoration: const InputDecoration(
                          hintText: 'ابحث في الصحابة...',
                          hintStyle: TextStyle(
                            color: Colors.white70,
                            fontSize: 24.0, // حجم ثابت 24 بكسل
                          ),
                          prefixIcon: Icon(Icons.search, color: Colors.white70),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Categories Filter
              Consumer2<CompanionsProvider, ThemeProvider>(
                builder: (context, provider, themeProvider, child) {
                  return Container(
                    height: 50,
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: provider.categories.length,
                      itemBuilder: (context, index) {
                        final category = provider.categories[index];
                        final isSelected = provider.selectedCategory == category;
                        return Container(
                          margin: const EdgeInsets.only(right: 10),
                          child: FilterChip(
                            label: Text(category),
                            selected: isSelected,
                            onSelected: (selected) {
                              provider.filterByCategory(category);
                            },
                            backgroundColor: themeProvider.isDarkMode
                                ? Colors.white.withValues(alpha: 0.2)
                                : Colors.black.withValues(alpha: 0.1),
                            selectedColor: themeProvider.isDarkMode
                                ? Colors.white.withValues(alpha: 0.3)
                                : Colors.black.withValues(alpha: 0.2),
                            labelStyle: TextStyle(
                              color: themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black87,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
              const SizedBox(height: 20),
              // Companions List
              Expanded(
                child: Consumer3<CompanionsProvider, FavoritesProvider, ThemeProvider>(
                  builder: (context, provider, favoritesProvider, themeProvider, child) {
                    return Container(
                      decoration: BoxDecoration(
                        color: themeProvider.isDarkMode
                            ? AppTheme.surfaceDark
                            : Colors.white,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                      ),
                      child: provider.companions.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.search_off,
                                    size: 64,
                                    color: themeProvider.isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'لا توجد نتائج',
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: themeProvider.isDarkMode
                                          ? Colors.grey[400]
                                          : Colors.grey
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.all(20),
                              itemCount: provider.companions.length,
                              itemBuilder: (context, index) {
                                final companion = provider.companions[index];
                                return _buildCompanionCard(context, companion, favoritesProvider);
                              },
                            ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompanionCard(BuildContext context, Companion companion, FavoritesProvider favoritesProvider) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          color: themeProvider.isDarkMode ? AppTheme.surfaceDark : Colors.white,
          child: InkWell(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CompanionDetailScreen(companion: companion),
              ),
            ),
            borderRadius: BorderRadius.circular(15),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: themeProvider.isDarkMode
                      ? [
                          AppTheme.primaryDark.withValues(alpha: 0.2),
                          AppTheme.primaryDark.withValues(alpha: 0.1),
                        ]
                      : [
                          Theme.of(context).primaryColor.withValues(alpha: 0.1),
                          Theme.of(context).primaryColor.withValues(alpha: 0.05),
                        ],
                ),
              ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          companion.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (companion.nickname.isNotEmpty)
                          Text(
                            companion.nickname,
                            style: TextStyle(
                              fontSize: 14,
                              color: themeProvider.isDarkMode
                                  ? const Color(0xFF81C784) // أخضر فاتح للوضع الليلي
                                  : Theme.of(context).primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () async {
                      // حفظ الحالة قبل التغيير
                      final wasInFavorites = favoritesProvider.isCompanionFavorite(companion.id);
                      await favoritesProvider.toggleCompanionFavorite(companion);
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              wasInFavorites
                                  ? 'تم إزالة ${companion.name} من المفضلة'
                                  : 'تم إضافة ${companion.name} للمفضلة',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                    icon: Icon(
                      favoritesProvider.isCompanionFavorite(companion.id) ? Icons.favorite : Icons.favorite_border,
                      color: favoritesProvider.isCompanionFavorite(companion.id) ? Colors.red : Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode
                      ? AppTheme.primaryDark.withValues(alpha: 0.3)
                      : Theme.of(context).primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  companion.category,
                  style: TextStyle(
                    fontSize: 12,
                    color: themeProvider.isDarkMode
                        ? const Color(0xFF81C784) // أخضر فاتح للوضع الليلي
                        : Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                companion.famousFor,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: themeProvider.isDarkMode
                      ? AppTheme.onSurfaceDark
                      : AppTheme.onSurfaceLight,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                companion.biography,
                style: TextStyle(
                  fontSize: 13,
                  color: themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
            ),
          ),
        );
      },
    );
  }


}
