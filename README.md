# تطبيق السيرة النبوية الشامل

تطبيق متكامل وشامل لتعليم السيرة النبوية الشريفة والأحاديث النبوية وسير الصحابة الكرام باستخدام Flutter.

## 🌟 المميزات الرئيسية

### 📚 المحتوى الشامل
- **30 حدث من السيرة النبوية** مرتبة زمنياً مع تفاصيل شاملة
- **50 حديث نبوي صحيح** مصنفة حسب المواضيع
- **18 صحابي من الصحابة الكرام** مع سيرهم المفصلة
- تصنيفات متنوعة: الخلفاء الراشدون، العشرة المبشرون، الأنصار، المهاجرون، أمهات المؤمنين

### 🎨 التصميم والواجهة
- **شاشة بداية متحركة** مع رسوم متحركة جميلة
- **عدادات متحركة** للأرقام والإحصائيات
- **تدرجات لونية جميلة** في جميع أنحاء التطبيق
- **تصميم متجاوب** يعمل على جميع الأحجام
- **وضع ليلي ونهاري** مع تباين عالي للمساعدة البصرية

### 🔍 البحث والتصفية
- **بحث متقدم** في جميع المحتويات
- **تصفية حسب الفئات** للأحاديث والصحابة
- **نظام المفضلة** لحفظ المحتوى المهم
- **كلمات مفتاحية** لسهولة الوصول للمحتوى

### 🔔 نظام الإشعارات
- **إشعارات ذكية** للمحتوى الجديد
- **إشعار ترحيبي** عند فتح التطبيق
- **عداد الإشعارات غير المقروءة** في شريط التطبيق
- **إدارة شاملة للإشعارات** مع إمكانية الحذف والقراءة

### ⚙️ الإعدادات المتقدمة
- **تخصيص حجم الخط** مع 8 مستويات مختلفة
- **اختيار نمط المظهر** (فاتح، مظلم، تلقائي)
- **وضع التباين العالي** لذوي الاحتياجات الخاصة
- **إحصائيات المحتوى** المفصلة
- **إعادة تعيين الإعدادات** للقيم الافتراضية

### 🚀 الأداء والتحسينات
- **تحميل سريع** للبيانات
- **ذاكرة تخزين محلية** للإعدادات والمفضلة
- **رسوم متحركة سلسة** ومحسنة
- **استهلاك ذاكرة منخفض**

## 🛠️ التقنيات المستخدمة

- **Flutter 3.x** - إطار العمل الأساسي
- **Provider Pattern** - إدارة الحالة
- **Material Design 3** - نظام التصميم
- **SharedPreferences** - التخزين المحلي
- **Custom Animations** - الرسوم المتحركة المخصصة

## 📱 الشاشات المتاحة

1. **الشاشة الرئيسية** - نظرة عامة مع إحصائيات متحركة
2. **السيرة النبوية** - أحداث مفصلة مع بحث وتصفية
3. **الأحاديث النبوية** - مجموعة شاملة مصنفة
4. **الصحابة الكرام** - سير مفصلة مع معلومات شاملة
5. **البحث** - بحث موحد في جميع المحتويات
6. **المفضلة** - المحتوى المحفوظ
7. **الإشعارات** - إدارة الإشعارات
8. **الإعدادات** - تخصيص التطبيق

## 🚀 كيفية التشغيل

### المتطلبات
- Flutter SDK 3.0 أو أحدث
- Dart 3.0 أو أحدث
- Android Studio أو VS Code
- متصفح ويب (للتشغيل على الويب)

### خطوات التشغيل
```bash
# 1. استنسخ المشروع
git clone [repository-url]

# 2. انتقل لمجلد المشروع
cd seerah_app_standalone

# 3. تحميل التبعيات
flutter pub get

# 4. تشغيل التطبيق
flutter run -d chrome  # للويب
flutter run -d android # للأندرويد
flutter run -d ios     # للآيفون
```

## 📊 إحصائيات المشروع

- **30** حدث من السيرة النبوية
- **50** حديث نبوي صحيح
- **18** صحابي من الصحابة الكرام
- **8** شاشات رئيسية
- **15+** ملف Dart
- **3** أنماط مظهر (فاتح، مظلم، تلقائي)
- **8** مستويات لحجم الخط

## 👨‍💻 المطور

**وائل شعيبي 2025**

---

*تطبيق السيرة النبوية - رحلة تعليمية شاملة في حياة النبي محمد ﷺ*
