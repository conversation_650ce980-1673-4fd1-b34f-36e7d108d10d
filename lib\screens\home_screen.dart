import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/companions_provider.dart';
import '../screens/companions_screen.dart';
import '../screens/seerah_screen.dart';
import '../screens/hadith_screen.dart';
import '../widgets/animated_counter.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1976D2),
            Color(0xFF42A5F5),
          ],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),
            // أيقونة المسجد الرئيسية
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.mosque,
                size: 80,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 20),

            // العنوان الرئيسي
            const Text(
              'سيرة النبي محمد ﷺ',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),

            // النص التوضيحي
            const Text(
              'تطبيق شامل لتعليم السيرة النبوية الشريفة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),

            // كروت الأقسام مع الإحصائيات
            Consumer4<SeerahProvider, HadithProvider, CompanionsProvider, AppProvider>(
              builder: (context, seerahProvider, hadithProvider, companionsProvider, appProvider, child) {
                return Column(
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const SeerahScreen()),
                      ),
                      child: _buildSectionCard(
                        icon: Icons.book_rounded,
                        title: 'السيرة النبوية',
                        subtitle: 'تعرف على أحداث حياة النبي ﷺ',
                        color: const Color(0xFF4CAF50),
                        count: seerahProvider.events.length,
                      ),
                    ),
                    const SizedBox(height: 16),

                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const HadithScreen()),
                      ),
                      child: _buildSectionCard(
                        icon: Icons.format_quote_rounded,
                        title: 'الأحاديث النبوية',
                        subtitle: 'مجموعة من الأحاديث الشريفة',
                        color: const Color(0xFFFF9800),
                        count: hadithProvider.hadiths.length,
                      ),
                    ),
                    const SizedBox(height: 16),

                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const CompanionsScreen()),
                      ),
                      child: _buildSectionCard(
                        icon: Icons.people_rounded,
                        title: 'الصحابة الكرام',
                        subtitle: 'تعرف على سير الصحابة الكرام',
                        color: const Color(0xFF9C27B0),
                        count: companionsProvider.companions.length,
                        isComingSoon: false,
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 40),

            // رسالة ترحيبية
            Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 40,
                      color: Color(0xFF1976D2),
                    ),
                    SizedBox(height: 12),
                    Text(
                      'مرحباً بك في تطبيق السيرة النبوية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF424242),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'استخدم شريط التنقل السفلي للانتقال بين الأقسام',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF757575),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    int count = 0,
    bool isComingSoon = false,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {}, // سيتم تجاهل هذا لأن GestureDetector في المستوى الأعلى
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  isComingSoon ? Colors.grey.shade400 : color,
                  isComingSoon ? Colors.grey.shade300 : color.withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                isComingSoon ? Icons.schedule : icon,
                size: 32,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      if (!isComingSoon && count > 0)
                        AnimatedCounter(
                          count: count,
                          textStyle: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isComingSoon ? Icons.schedule : Icons.arrow_forward_ios,
              color: Colors.white,
              size: 20,
            ),
          ],
        ),
          ),
        ),
      ),
    );
  }
}
