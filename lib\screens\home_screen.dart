import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/seerah_provider.dart';
import '../providers/hadith_provider.dart';
import '../providers/companions_provider.dart';
import '../screens/companions_screen.dart';
import '../screens/seerah_screen.dart';
import '../screens/hadith_screen.dart';
import '../widgets/animated_counter.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF102c23), // أخضر داكن جداً
            Color(0xFF183f33), // أخضر داكن
            Color(0xFF123127), // أخضر متوسط
            Color(0xFF113026), // أخضر فاتح نسبياً
          ],
          stops: [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const SizedBox(height: 20),
            // أيقونة التطبيق الرئيسية SVG
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.asset(
                  'assets/icon.png',
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // العنوان الرئيسي
            const Text(
              'سيرة النبي محمد ﷺ',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),

            // النص التوضيحي
            const Text(
              'تطبيق شامل لتعليم السيرة النبوية الشريفة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),

            // كروت الأقسام مع الإحصائيات
            Consumer4<SeerahProvider, HadithProvider, CompanionsProvider, AppProvider>(
              builder: (context, seerahProvider, hadithProvider, companionsProvider, appProvider, child) {
                return Column(
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const SeerahScreen()),
                      ),
                      child: _buildSectionCard(
                        icon: Icons.book_rounded,
                        title: 'السيرة النبوية',
                        subtitle: 'تعرف على أحداث حياة النبي ﷺ',
                        color: const Color(0xFF4CAF50),
                        count: seerahProvider.events.length,
                      ),
                    ),
                    const SizedBox(height: 16),

                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const HadithScreen()),
                      ),
                      child: _buildSectionCard(
                        icon: Icons.format_quote_rounded,
                        title: 'الأحاديث النبوية',
                        subtitle: 'مجموعة من الأحاديث الشريفة',
                        color: const Color(0xFFFF9800),
                        count: hadithProvider.hadiths.length,
                      ),
                    ),
                    const SizedBox(height: 16),

                    GestureDetector(
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const CompanionsScreen()),
                      ),
                      child: _buildSectionCard(
                        icon: Icons.people_rounded,
                        title: 'الصحابة الكرام',
                        subtitle: 'تعرف على سير الصحابة الكرام',
                        color: const Color(0xFF9C27B0),
                        count: companionsProvider.companions.length,
                        isComingSoon: false,
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    int count = 0,
    bool isComingSoon = false,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              isComingSoon ? Colors.grey.shade400 : color,
              isComingSoon ? Colors.grey.shade300 : color.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                isComingSoon ? Icons.schedule : icon,
                size: 32,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      if (!isComingSoon && count > 0)
                        AnimatedCounter(
                          count: count,
                          textStyle: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isComingSoon ? Icons.schedule : Icons.arrow_forward_ios,
              color: Colors.white,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
