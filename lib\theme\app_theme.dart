import 'package:flutter/material.dart';

class AppTheme {
  // الألوان الأساسية - الوضع النهاري
  static const Color primaryLight = Color(0xFF1976D2);
  static const Color primaryVariantLight = Color(0xFF1565C0);
  static const Color secondaryLight = Color(0xFF7B1FA2);
  static const Color secondaryVariantLight = Color(0xFF6A1B9A);

  // ألوان الخلفية - الوضع النهاري
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color scaffoldBackgroundLight = Color(0xFFF5F5F5);

  // ألوان النصوص - الوضع النهاري
  static const Color onPrimaryLight = Color(0xFFFFFFFF);
  static const Color onSecondaryLight = Color(0xFFFFFFFF);
  static const Color onBackgroundLight = Color(0xFF212121);
  static const Color onSurfaceLight = Color(0xFF424242);

  // الألوان الأساسية - الوضع الليلي
  static const Color primaryDark = Color(0xFF42A5F5);
  static const Color primaryVariantDark = Color(0xFF2196F3);
  static const Color secondaryDark = Color(0xFFAB47BC);
  static const Color secondaryVariantDark = Color(0xFF9C27B0);

  // ألوان الخلفية - الوضع الليلي
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  static const Color scaffoldBackgroundDark = Color(0xFF0A0A0A);

  // ألوان النصوص - الوضع الليلي
  static const Color onPrimaryDark = Color(0xFF000000);
  static const Color onSecondaryDark = Color(0xFF000000);
  static const Color onBackgroundDark = Color(0xFFE0E0E0);
  static const Color onSurfaceDark = Color(0xFFBDBDBD);

  // ألوان الحالة (متسقة في كلا الوضعين)
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // ألوان التفاعل
  static const Color hover = Color(0x1A1976D2);
  static const Color pressed = Color(0x331976D2);
  static const Color disabled = Color(0x611976D2);

  // التدرجات اللونية - الوضع النهاري
  static const List<Color> primaryGradientLight = [
    primaryLight,
    primaryVariantLight,
    Color(0xFF0D47A1),
  ];

  static const List<Color> secondaryGradientLight = [
    secondaryLight,
    secondaryVariantLight,
    Color(0xFF4A148C),
  ];

  // التدرجات اللونية - الوضع الليلي
  static const List<Color> primaryGradientDark = [
    primaryDark,
    primaryVariantDark,
    primaryLight,
  ];

  static const List<Color> secondaryGradientDark = [
    secondaryDark,
    secondaryVariantDark,
    secondaryLight,
  ];

  // أحجام الخطوط
  static const double headline1Size = 32.0;
  static const double headline2Size = 28.0;
  static const double headline3Size = 24.0;
  static const double headline4Size = 20.0;
  static const double headline5Size = 18.0;
  static const double headline6Size = 16.0;
  static const double bodyText1Size = 16.0;
  static const double bodyText2Size = 14.0;
  static const double subtitle1Size = 16.0;
  static const double subtitle2Size = 14.0;
  static const double captionSize = 12.0;
  static const double buttonSize = 14.0;
  static const double overlineSize = 10.0;

  // التباعد
  static const double extraSmallPadding = 4.0;
  static const double smallPadding = 8.0;
  static const double mediumPadding = 16.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  // الحدود المستديرة
  static BorderRadius smallRadius = BorderRadius.circular(8.0);
  static BorderRadius mediumRadius = BorderRadius.circular(12.0);
  static BorderRadius largeRadius = BorderRadius.circular(16.0);
  static BorderRadius extraLargeRadius = BorderRadius.circular(24.0);

  // أحجام الأيقونات
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;

  // مدة الحركات
  static const Duration fastAnimation = Duration(milliseconds: 150);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);
  static const Duration extraSlowAnimation = Duration(milliseconds: 800);
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration counterAnimation = Duration(milliseconds: 1500);
  static const Duration fadeAnimation = Duration(milliseconds: 400);

  // الثيم الفاتح
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: primaryLight,
      scaffoldBackgroundColor: scaffoldBackgroundLight,

      colorScheme: const ColorScheme.light(
        primary: primaryLight,
        primaryContainer: primaryVariantLight,
        secondary: secondaryLight,
        secondaryContainer: secondaryVariantLight,
        surface: surfaceLight,
        error: error,
        onPrimary: onPrimaryLight,
        onSecondary: onSecondaryLight,
        onSurface: onSurfaceLight,
        onError: Colors.white,
      ),

      appBarTheme: const AppBarTheme(
        backgroundColor: primaryLight,
        foregroundColor: onPrimaryLight,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: headline5Size,
          fontWeight: FontWeight.bold,
          color: onPrimaryLight,
        ),
      ),

      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: mediumRadius,
        ),
        color: surfaceLight,
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryLight,
          foregroundColor: onPrimaryLight,
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: smallRadius,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: mediumPadding,
            vertical: smallPadding,
          ),
        ),
      ),

      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceLight,
        selectedItemColor: primaryLight,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: headline1Size,
          fontWeight: FontWeight.bold,
          color: onBackgroundLight,
        ),
        headlineMedium: TextStyle(
          fontSize: headline2Size,
          fontWeight: FontWeight.bold,
          color: onBackgroundLight,
        ),
        headlineSmall: TextStyle(
          fontSize: headline3Size,
          fontWeight: FontWeight.bold,
          color: onBackgroundLight,
        ),
        titleLarge: TextStyle(
          fontSize: headline4Size,
          fontWeight: FontWeight.w600,
          color: onBackgroundLight,
        ),
        titleMedium: TextStyle(
          fontSize: headline5Size,
          fontWeight: FontWeight.w500,
          color: onBackgroundLight,
        ),
        titleSmall: TextStyle(
          fontSize: headline6Size,
          fontWeight: FontWeight.w500,
          color: onBackgroundLight,
        ),
        bodyLarge: TextStyle(
          fontSize: bodyText1Size,
          color: onBackgroundLight,
        ),
        bodyMedium: TextStyle(
          fontSize: bodyText2Size,
          color: onSurfaceLight,
        ),
        labelLarge: TextStyle(
          fontSize: buttonSize,
          fontWeight: FontWeight.w500,
          color: onBackgroundLight,
        ),
        bodySmall: TextStyle(
          fontSize: captionSize,
          color: Colors.grey,
        ),
      ),
    );
  }

  // الثيم المظلم
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: primaryDark,
      scaffoldBackgroundColor: scaffoldBackgroundDark,

      colorScheme: const ColorScheme.dark(
        primary: primaryDark,
        primaryContainer: primaryVariantDark,
        secondary: secondaryDark,
        secondaryContainer: secondaryVariantDark,
        surface: surfaceDark,
        error: error,
        onPrimary: onPrimaryDark,
        onSecondary: onSecondaryDark,
        onSurface: onSurfaceDark,
        onError: Colors.white,
      ),

      appBarTheme: const AppBarTheme(
        backgroundColor: primaryDark,
        foregroundColor: onPrimaryDark,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: headline5Size,
          fontWeight: FontWeight.bold,
          color: onPrimaryDark,
        ),
      ),

      cardTheme: CardTheme(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: mediumRadius,
        ),
        color: surfaceDark,
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryDark,
          foregroundColor: onPrimaryDark,
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: smallRadius,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: mediumPadding,
            vertical: smallPadding,
          ),
        ),
      ),

      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceDark,
        selectedItemColor: primaryDark,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: headline1Size,
          fontWeight: FontWeight.bold,
          color: onBackgroundDark,
        ),
        headlineMedium: TextStyle(
          fontSize: headline2Size,
          fontWeight: FontWeight.bold,
          color: onBackgroundDark,
        ),
        headlineSmall: TextStyle(
          fontSize: headline3Size,
          fontWeight: FontWeight.bold,
          color: onBackgroundDark,
        ),
        titleLarge: TextStyle(
          fontSize: headline4Size,
          fontWeight: FontWeight.w600,
          color: onBackgroundDark,
        ),
        titleMedium: TextStyle(
          fontSize: headline5Size,
          fontWeight: FontWeight.w500,
          color: onBackgroundDark,
        ),
        titleSmall: TextStyle(
          fontSize: headline6Size,
          fontWeight: FontWeight.w500,
          color: onBackgroundDark,
        ),
        bodyLarge: TextStyle(
          fontSize: bodyText1Size,
          color: onBackgroundDark,
        ),
        bodyMedium: TextStyle(
          fontSize: bodyText2Size,
          color: onSurfaceDark,
        ),
        labelLarge: TextStyle(
          fontSize: buttonSize,
          fontWeight: FontWeight.w500,
          color: onBackgroundDark,
        ),
        bodySmall: TextStyle(
          fontSize: captionSize,
          color: Colors.grey,
        ),
      ),
    );
  }

  // دوال مساعدة للتدرجات
  static LinearGradient getPrimaryGradient(bool isDark) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: isDark ? primaryGradientDark : primaryGradientLight,
    );
  }

  static LinearGradient getSecondaryGradient(bool isDark) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: isDark ? secondaryGradientDark : secondaryGradientLight,
    );
  }

  // ظلال الكروت
  static List<BoxShadow> get lightShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      blurRadius: 8.0,
      spreadRadius: 2.0,
      offset: const Offset(0, 4),
    ),
  ];

  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      blurRadius: 12.0,
      spreadRadius: 3.0,
      offset: const Offset(0, 6),
    ),
  ];

  static List<BoxShadow> get heavyShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.2),
      blurRadius: 16.0,
      spreadRadius: 4.0,
      offset: const Offset(0, 8),
    ),
  ];
}
