import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';
import '../models/reward.dart';

class RewardsProvider with ChangeNotifier {
  UserStats _userStats = UserStats(
    totalPoints: 0,
    currentStreak: 0,
    longestStreak: 0,
    lastVisit: DateTime.now(),
    firstVisit: DateTime.now(),
    totalVisits: 0,
    earnedRewards: [],
  );

  List<Reward> _recentRewards = [];
  bool _isLoading = false;
  bool _hasNewReward = false;
  Reward? _latestReward;

  // Getters
  UserStats get userStats => _userStats;
  List<Reward> get recentRewards => _recentRewards;
  bool get isLoading => _isLoading;
  bool get hasNewReward => _hasNewReward;
  Reward? get latestReward => _latestReward;

  // تحميل البيانات
  Future<void> loadUserStats() async {
    _isLoading = true;

    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل إحصائيات المستخدم
      final statsJson = prefs.getString('user_stats');
      if (statsJson != null) {
        _userStats = UserStats.fromJson(json.decode(statsJson));
      }

      // تحميل المكافآت الأخيرة
      final rewardsJson = prefs.getString('recent_rewards');
      if (rewardsJson != null) {
        final List<dynamic> rewardsList = json.decode(rewardsJson);
        _recentRewards = rewardsList.map((r) => Reward.fromJson(r)).toList();
      }

    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المكافآت: $e');
    }

    _isLoading = false;
  }

  // حفظ البيانات
  Future<void> _saveUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ إحصائيات المستخدم
      await prefs.setString('user_stats', json.encode(_userStats.toJson()));

      // حفظ المكافآت الأخيرة (آخر 10 مكافآت)
      final recentRewardsToSave = _recentRewards.take(10).toList();
      await prefs.setString('recent_rewards',
          json.encode(recentRewardsToSave.map((r) => r.toJson()).toList()));

    } catch (e) {
      debugPrint('خطأ في حفظ بيانات المكافآت: $e');
    }
  }

  // فحص وإضافة مكافآت عند دخول التطبيق
  Future<void> checkAndAddDailyReward() async {
    final now = DateTime.now();
    final lastVisit = _userStats.lastVisit;

    // التحقق من أول زيارة
    if (_userStats.totalVisits == 0) {
      await _addFirstTimeReward();
      return;
    }

    // التحقق من الزيارة اليومية
    final daysDifference = now.difference(lastVisit).inDays;

    if (daysDifference >= 1) {
      // زيارة جديدة
      int newStreak = daysDifference == 1 ? _userStats.currentStreak + 1 : 1;

      // تحديث الإحصائيات
      _userStats = _userStats.copyWith(
        lastVisit: now,
        totalVisits: _userStats.totalVisits + 1,
        currentStreak: newStreak,
        longestStreak: max(_userStats.longestStreak, newStreak),
      );

      // إضافة المكافأة المناسبة
      await _addDailyReward(newStreak);

      // حفظ البيانات
      await _saveUserStats();
    }
  }

  // إضافة مكافأة أول مرة
  Future<void> _addFirstTimeReward() async {
    final reward = Reward(
      id: 'first_time_${DateTime.now().millisecondsSinceEpoch}',
      title: 'مرحباً بك في التطبيق! 🌟',
      description: 'شكراً لك على تحميل التطبيق. ابدأ رحلتك في تعلم السيرة النبوية!',
      points: RewardConstants.firstTimePoints,
      type: RewardType.firstTime,
      earnedAt: DateTime.now(),
      icon: RewardConstants.rewardIcons[RewardType.firstTime]!,
      color: RewardConstants.rewardColors[RewardType.firstTime]!,
    );

    await _addReward(reward);

    // تحديث الإحصائيات
    _userStats = _userStats.copyWith(
      firstVisit: DateTime.now(),
      lastVisit: DateTime.now(),
      totalVisits: 1,
      currentStreak: 1,
      longestStreak: 1,
    );

    await _saveUserStats();
  }

  // إضافة مكافأة يومية
  Future<void> _addDailyReward(int streak) async {
    String title;
    String description;
    int points;
    RewardType type;

    if (streak >= 30) {
      // مكافأة شهرية
      title = 'إنجاز شهري رائع! 👑';
      description = 'لقد واصلت الزيارة لمدة شهر كامل! مثابرة مميزة';
      points = RewardConstants.monthlyStreakPoints;
      type = RewardType.monthly;
    } else if (streak >= 7) {
      // مكافأة أسبوعية
      title = 'أسبوع من المثابرة! 🏆';
      description = 'لقد زرت التطبيق لمدة أسبوع متتالي! استمر في التميز';
      points = RewardConstants.weeklyStreakPoints;
      type = RewardType.weekly;
    } else if (streak > 1) {
      // مكافأة متتالية
      title = 'مثابرة رائعة! 🔥';
      description = 'لقد زرت التطبيق $streak أيام متتالية! واصل التقدم';
      points = RewardConstants.dailyPoints + (streak * 2);
      type = RewardType.streak;
    } else {
      // مكافأة يومية عادية
      title = _getRandomMotivationalQuote();
      description = 'مكافأة يومية لزيارتك التطبيق. استمر في طلب العلم!';
      points = RewardConstants.dailyPoints;
      type = RewardType.daily;
    }

    final reward = Reward(
      id: 'daily_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      description: description,
      points: points,
      type: type,
      earnedAt: DateTime.now(),
      icon: RewardConstants.rewardIcons[type]!,
      color: RewardConstants.rewardColors[type]!,
    );

    await _addReward(reward);
  }

  // إضافة مكافأة
  Future<void> _addReward(Reward reward) async {
    // إضافة النقاط
    _userStats = _userStats.copyWith(
      totalPoints: _userStats.totalPoints + reward.points,
      earnedRewards: [..._userStats.earnedRewards, reward.id],
    );

    // إضافة المكافأة للقائمة
    _recentRewards.insert(0, reward);

    // تحديد المكافأة الجديدة
    _latestReward = reward;
    _hasNewReward = true;

    // تأجيل notifyListeners إلى ما بعد انتهاء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  // الحصول على عبارة تحفيزية عشوائية
  String _getRandomMotivationalQuote() {
    final random = Random();
    return RewardConstants.motivationalQuotes[
        random.nextInt(RewardConstants.motivationalQuotes.length)];
  }

  // إخفاء المكافأة الجديدة
  void hideNewReward() {
    _hasNewReward = false;
    _latestReward = null;
    notifyListeners();
  }

  // الحصول على مستوى المستخدم
  int getUserLevel() {
    return (_userStats.totalPoints / 100).floor() + 1;
  }

  // الحصول على النقاط المطلوبة للمستوى التالي
  int getPointsToNextLevel() {
    final currentLevel = getUserLevel();
    final pointsForNextLevel = currentLevel * 100;
    return pointsForNextLevel - _userStats.totalPoints;
  }

  // الحصول على نسبة التقدم للمستوى التالي
  double getLevelProgress() {
    final currentLevel = getUserLevel();
    final pointsForCurrentLevel = (currentLevel - 1) * 100;
    final pointsForNextLevel = currentLevel * 100;
    final currentProgress = _userStats.totalPoints - pointsForCurrentLevel;
    final totalProgress = pointsForNextLevel - pointsForCurrentLevel;
    return currentProgress / totalProgress;
  }

  // إعادة تعيين البيانات (للاختبار)
  Future<void> resetUserStats() async {
    _userStats = UserStats(
      totalPoints: 0,
      currentStreak: 0,
      longestStreak: 0,
      lastVisit: DateTime.now(),
      firstVisit: DateTime.now(),
      totalVisits: 0,
      earnedRewards: [],
    );
    _recentRewards.clear();
    _hasNewReward = false;
    _latestReward = null;

    await _saveUserStats();
    notifyListeners();
  }
}
