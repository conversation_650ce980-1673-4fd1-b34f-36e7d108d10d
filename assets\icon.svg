<!--
هذا ملف SVG مؤقت. يجب استبداله بـ SVG محول من الأيقونة الأصلية.
لتحويل icon.png إلى SVG، استخدم أحد الحلول التالية:

1. أدوات التحويل الأونلاين:
   - https://convertio.co/png-svg/
   - https://www.pngtosvg.com/
   - https://image.online-convert.com/convert-to-svg

2. برامج التصميم:
   - Adobe Illustrator (Image Trace)
   - Inkscape (مجاني)
   - GIMP مع إضافة SVG

3. أدوات سطر الأوامر:
   - potrace
   - autotrace

بعد التحويل، استبدل محتوى هذا الملف بـ SVG المحول.
-->

<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- ملف SVG مؤقت - يجب استبداله بالأيقونة الأصلية المحولة -->
  <defs>
    <radialGradient id="tempGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </radialGradient>
  </defs>

  <!-- خلفية مؤقتة -->
  <circle cx="256" cy="256" r="240" fill="url(#tempGradient)" stroke="#1B5E20" stroke-width="8"/>

  <!-- نص تنبيه -->
  <text x="256" y="200" text-anchor="middle" fill="white" font-size="24" font-weight="bold">
    استبدل هذا الملف
  </text>
  <text x="256" y="240" text-anchor="middle" fill="white" font-size="20">
    بـ SVG محول من
  </text>
  <text x="256" y="280" text-anchor="middle" fill="white" font-size="20">
    icon.png الأصلية
  </text>

  <!-- أيقونة مؤقتة -->
  <circle cx="256" cy="350" r="50" fill="white" fill-opacity="0.3"/>
  <text x="256" y="360" text-anchor="middle" fill="white" font-size="40">📱</text>
</svg>
