                        -HD:\def\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\AndroidstudioSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\AndroidstudioSDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\AndroidstudioSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\uuu\test1\seerah_app_new\build\app\intermediates\cxx\Debug\6x402d18\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\uuu\test1\seerah_app_new\build\app\intermediates\cxx\Debug\6x402d18\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\uuu\test1\seerah_app_new\android\app\.cxx\Debug\6x402d18\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2